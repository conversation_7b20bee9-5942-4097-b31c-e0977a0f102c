import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_theme_color.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

/// TextStyle 扩展：链式设置字体大小、字重、字体族
/// Extension on TextStyle for chaining font size, weight and family
extension CustomFontExtension on TextStyle {
  /* 字号扩展 Font size extensions (scaled with .gsp) */
  TextStyle get fs12 => copyWith(fontSize: 12.gsp);
  TextStyle get fs13 => copyWith(fontSize: 13.gsp);
  TextStyle get fs15 => copyWith(fontSize: 15.gsp);
  TextStyle get fs16 => copyWith(fontSize: 16.gsp);
  TextStyle get fs17 => copyWith(fontSize: 17.gsp);
  TextStyle get fs18 => copyWith(fontSize: 18.gsp);
  TextStyle get fs19 => copyWith(fontSize: 19.gsp);
  TextStyle get fs20 => copyWith(fontSize: 20.gsp);
  TextStyle get fs21 => copyWith(fontSize: 21.gsp);
  TextStyle get fs22 => copyWith(fontSize: 22.gsp);
  TextStyle get fs23 => copyWith(fontSize: 23.gsp);
  TextStyle get fs24 => copyWith(fontSize: 24.gsp);

  /* 字重扩展 Font weight extensions */
  TextStyle get w300 => copyWith(fontWeight: FontWeight.w300);
  TextStyle get w400 => copyWith(fontWeight: FontWeight.w400);
  TextStyle get w500 => copyWith(fontWeight: FontWeight.w500);
  TextStyle get w600 => copyWith(fontWeight: FontWeight.w600);
  TextStyle get w700 => copyWith(fontWeight: FontWeight.w700);
  TextStyle get w800 => copyWith(fontWeight: FontWeight.w800);

  /* 字体族扩展 Font family extensions */
  TextStyle get ffAkz => copyWith(fontFamily: 'Akzidenz-Grotesk');
  TextStyle get ffImpact => copyWith(fontFamily: 'Impact'); // 仅用于数字 Only for numbers
}

/// 自定义文字主题，用于统一管理多套风格和颜色
/// Custom text theme to manage font styles & colors across themes
///
/// 可通过 [context.colorTheme] 获取当前主题颜色集
/// Access via [context.colorTheme] in your widgets
class CustomTextTheme extends ThemeExtension<CustomTextTheme> {
  final TextStyle primary; // 主要文字 Main text
  final TextStyle regular; // 常规文字 Secondary text
  final TextStyle active; // 功能激活文字 Active state text
  final TextStyle button; // 按钮字体 Button text
  final TextStyle stockRed; // 股票红色 Stock red
  final TextStyle stockGreen; // 股票绿色 Stock green
  final TextStyle pending; // 中性状态（待审核等） Neutral status (e.g. pending)

  const CustomTextTheme({
    required this.primary,
    required this.regular,
    required this.active,
    required this.button,
    required this.stockRed,
    required this.stockGreen,
    required this.pending,
  });

  /// 工厂构建函数：根据 ThemeColor 构建 TextStyle
  factory CustomTextTheme.fromColor(CustomColorTheme colorSet) {
    return CustomTextTheme(
      primary: TextStyle(fontSize: 14.gsp, color: colorSet.textPrimary),
      regular: TextStyle(fontSize: 14.gsp, color: colorSet.textRegular),
      active: TextStyle(fontSize: 14.gsp, color: colorSet.tabActive),
      button: TextStyle(fontSize: 14.gsp, color: colorSet.buttonPrimary, fontWeight: FontWeight.w500),
      stockRed: TextStyle(fontSize: 14.gsp, color: colorSet.stockRed),
      stockGreen: TextStyle(fontSize: 14.gsp, color: colorSet.stockGreen),
      pending: TextStyle(fontSize: 14.gsp, color: colorSet.pending),
    );
  }

  /// 亮色主题样式 A
  /// Light theme variant A
  static CustomTextTheme gpLightA = CustomTextTheme.fromColor(CustomColorTheme.gpLightA);

  /// 暗色主题样式 A
  /// Dark theme variant A
  static CustomTextTheme gpDarkA = CustomTextTheme.fromColor(CustomColorTheme.gpDarkA);

  /// 新增风格可在此处继续扩展（如 gpLightB、gpDarkB）
  /// Add more theme variants below (e.g. gpLightB, gpDarkB)

  /// 拷贝样式 Copy current theme with overrides
  @override
  CustomTextTheme copyWith({
    TextStyle? primary,
    TextStyle? regular,
    TextStyle? active,
    TextStyle? button,
    TextStyle? stockRed,
    TextStyle? stockGreen,
    TextStyle? pending,
  }) {
    return CustomTextTheme(
      primary: primary ?? this.primary,
      regular: regular ?? this.regular,
      active: active ?? this.active,
      button: button ?? this.button,
      stockRed: stockRed ?? this.stockRed,
      stockGreen: stockGreen ?? this.stockGreen,
      pending: pending ?? this.pending,
    );
  }

  /// 主题动画渐变插值，用于主题切换动画
  /// Interpolation for theme switching animation
  @override
  CustomTextTheme lerp(ThemeExtension<CustomTextTheme>? other, double t) {
    if (other is! CustomTextTheme) return this;
    return CustomTextTheme(
      primary: TextStyle.lerp(primary, other.primary, t)!,
      regular: TextStyle.lerp(regular, other.regular, t)!,
      active: TextStyle.lerp(active, other.active, t)!,
      button: TextStyle.lerp(button, other.button, t)!,
      stockRed: TextStyle.lerp(stockRed, other.stockRed, t)!,
      stockGreen: TextStyle.lerp(stockGreen, other.stockGreen, t)!,
      pending: TextStyle.lerp(pending, other.pending, t)!,
    );
  }
}
