import 'package:flutter/material.dart';
import 'package:game_store/core/utils/font_size.dart';

// Light theme
final ThemeData lightTheme = ThemeData.light().copyWith(
  brightness: Brightness.light,
  primaryColor: const Color(0xFFB9936D), // 主题色
  primaryColorLight: const Color(0xFFe7cd91), // 主题色-浅色
  scaffoldBackgroundColor: const Color(0xffeaedf5), // Scaffold的默认背景颜色。
  hintColor: const Color(0xff7F8A9B), // 用于提示文本或占位符文本的颜色
  splashColor: Colors.transparent, // 点击时的高亮效果
  highlightColor: Colors.transparent, // 长按时的扩散效果
  unselectedWidgetColor: const Color(0xffE9EDF3), // 用于未选中的复选框
  disabledColor: const Color(0xff9BA6B6), // 禁用状态下部件的颜色
  hoverColor: const Color(0xffE7E7E7), // 鼠标悬停时的颜色
  shadowColor: const Color(0xffF5F6FA), // 阴影颜色
  focusColor: const Color(0xffFFF2E8), // 焦点状态下的颜色
  canvasColor: Colors.transparent, // 用于画布背景颜色
  dividerColor: const Color.fromRGBO(38, 77, 137, 0.1), // 分割线
  appBarTheme: const AppBarTheme(
    backgroundColor: Colors.white, // AppBar背景色
    titleTextStyle: TextStyle(color: Color(0xff6A7391)), // AppBarTitle文字颜色
    iconTheme: IconThemeData(color: Color(0xff6A7391)), // AppBar图标颜色
  ),
  bottomNavigationBarTheme: const BottomNavigationBarThemeData(
    backgroundColor: Colors.white,
    selectedItemColor: Color(0xFFa98e50),
    unselectedItemColor: Color(0xff585859),
  ),
  drawerTheme: const DrawerThemeData().copyWith(
    shape: null,
  ),
  tabBarTheme: TabBarTheme(
    splashFactory: NoSplash.splashFactory, // 取消水波纹
    overlayColor: WidgetStateProperty.all(Colors.transparent), // 设置点击时的阴影颜色为透明
    dividerColor: Colors.transparent, //
  ),
  cardTheme:const CardTheme(

  ),
  buttonTheme: const ButtonThemeData(
    splashColor: Colors.transparent, // 点击时的高亮效果
    highlightColor: Colors.transparent, // 长按时的扩散效果
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ButtonStyle(
      elevation: WidgetStateProperty.all(0),  // 去除 ElevatedButton 阴影
    ),
  ),
  textTheme: TextTheme(
    displayLarge: TextStyle(fontSize: 24.fs, color: const Color(0xff000000), fontWeight: FontWeight.w500),
    displayMedium: TextStyle(fontSize: 22.fs, color: const Color(0xff000000), fontWeight: FontWeight.w500),
    displaySmall: TextStyle(fontSize: 20.fs, color: const Color(0xff000000), fontWeight: FontWeight.w500),
    headlineLarge: TextStyle(fontSize: 20.fs, color: const Color(0xff3B4165), fontWeight: FontWeight.w500),
    headlineMedium: TextStyle(fontSize: 18.fs, color: const Color(0xff3B4165), fontWeight: FontWeight.w500),
    headlineSmall: TextStyle(fontSize: 16.fs, color: const Color(0xff3B4165), fontWeight: FontWeight.w500),
    titleLarge: TextStyle(fontSize: 16.fs, color: const Color(0xff383C48)),
    titleMedium: TextStyle(fontSize: 14.fs, color: const Color(0xff383C48)),
    titleSmall: TextStyle(fontSize: 12.fs, color: const Color(0xff383C48)),
    bodyLarge: TextStyle(fontSize: 14.fs, color: const Color(0xff666666)),
    bodyMedium: TextStyle(fontSize: 12.fs, color: const Color(0xff666666)),
    bodySmall: TextStyle(fontSize: 10.fs, color: const Color(0xff666666)),
    labelLarge: TextStyle(fontSize: 12.fs, color: const Color(0xff8696A2)),
    labelMedium: TextStyle(fontSize: 10.fs, color: const Color(0xff8696A2)),
    labelSmall: TextStyle(fontSize: 8.fs, color: const Color(0xff8696A2)),
  ),
  inputDecorationTheme: const InputDecorationTheme(
    hintStyle: TextStyle(
      color: Color(0xff808C9F), // 设置 hintText 的颜色
    ),
  ),
  colorScheme: ColorScheme.fromSwatch().copyWith(
    surface: Colors.white,
    onSurface: Colors.black,
    primary: const Color(0xFFa98e50),
    onPrimary: Colors.orange,
    secondary: const Color(0xFFe7cd91),
    onSecondary: Colors.black,
    error: Colors.red,
    onError: Colors.white,
    brightness: Brightness.light,
  ),
);

/// Not configured 未配置
// Dark theme
final ThemeData darkTheme = ThemeData.dark().copyWith(
  brightness: Brightness.dark,
  primaryColor: const Color(0xFFa98e50),
  // 主题色
  primaryColorLight: const Color(0xFFe7cd91),
  // 主题色-浅色
  scaffoldBackgroundColor: const Color(0xfff0f3f9),
  // Scaffold的默认背景颜色。
  hintColor: const Color(0xff7F8A9B),
  // 用于提示文本或占位符文本的颜色
  splashColor: Colors.transparent,
  // 点击时的高亮效果
  highlightColor: Colors.transparent,
  // 长按时的扩散效果
  unselectedWidgetColor: const Color(0xffE9EDF3),
  // 用于未选中的复选框
  disabledColor: const Color(0xff9BA6B6),
  // 禁用状态下部件的颜色
  hoverColor: const Color(0xffE7E7E7),
  // 鼠标悬停时的颜色
  shadowColor: const Color(0xffF5F6FA),
  // 阴影颜色
  focusColor: const Color(0xffFFF2E8),
  // 焦点状态下的颜色
  canvasColor: const Color(0xffBBD3FB),
  // 用于画布背景颜色
  dividerColor: const Color.fromRGBO(38, 77, 137, 0.1),
  // 分割线
  bottomNavigationBarTheme: const BottomNavigationBarThemeData(
    backgroundColor: Colors.white,
    selectedItemColor: Color(0xFFa98e50),
    unselectedItemColor: Color(0xff585859),
  ),
  textTheme: TextTheme(
    headlineLarge: TextStyle(fontSize: 20.fs, color: const Color(0xff333333), fontWeight: FontWeight.bold),
    headlineMedium: TextStyle(fontSize: 18.fs, color: const Color(0xff333333), fontWeight: FontWeight.bold),
    headlineSmall: TextStyle(fontSize: 16.fs, color: const Color(0xff333333), fontWeight: FontWeight.bold),
    titleLarge: TextStyle(fontSize: 16.fs, color: const Color(0xff383C48)),
    titleMedium: TextStyle(fontSize: 14.fs, color: const Color(0xff383C48)),
    titleSmall: TextStyle(fontSize: 12.fs, color: const Color(0xff383C48)),
    bodyLarge: TextStyle(fontSize: 14.fs, color: const Color(0xff585C66)),
    bodyMedium: TextStyle(fontSize: 12.fs, color: const Color(0xff585C66)),
    bodySmall: TextStyle(fontSize: 10.fs, color: const Color(0xff585C66)),
    labelLarge: TextStyle(fontSize: 12.fs, color: const Color(0xff585859)),
    labelMedium: TextStyle(fontSize: 10.fs, color: const Color(0xff585859)),
    labelSmall: TextStyle(fontSize: 8.fs, color: const Color(0xff585859)),
  ),
);
