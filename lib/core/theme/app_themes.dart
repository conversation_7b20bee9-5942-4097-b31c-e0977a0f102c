import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/theme/custom_theme_color.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

/// BuildContext 扩展：快速访问主题
/// Extension on BuildContext for quick access to Theme
extension CustomTextThemeExtension on BuildContext {
  /// 获取当前主题
  /// Get current ThemeData
  ThemeData get theme => Theme.of(this);

  /// 获取当前自定义文字主题
  /// Get current CustomTextTheme from ThemeExtensions
  CustomTextTheme get textTheme => Theme.of(this).extension<CustomTextTheme>()!;

  /// 获取当前自定义颜色主题
  /// Get current CustomColorTheme from ThemeExtensions
  CustomColorTheme get colorTheme => Theme.of(this).extension<CustomColorTheme>()!;
}

/// app主题相关，使用context.theme调用该主题
class AppTheme {
  AppTheme._();
  static final AppTheme instance = AppTheme._();
  ThemeData gpThemeLightA = ThemeData.light().copyWith(
    primaryColor: const Color(0xFFDDB274), // 主题色
    primaryColorLight: const Color(0xFFF6D199), // 主题色-浅色
    scaffoldBackgroundColor: const Color(0xffF7F8F8), // Scaffold的默认背景颜色。
    hintColor: const Color(0xFFCBC0AF), // 用于提示文本或占位符文本的颜色
    splashColor: Colors.transparent, // 点击时的高亮效果
    highlightColor: Colors.transparent, // 长按时的扩散效果
    unselectedWidgetColor: const Color(0xffE9EDF3), // 用于未选中的复选框
    disabledColor: const Color(0xff9BA6B6), // 禁用状态下部件的颜色
    hoverColor: const Color(0xffE7E7E7), // 鼠标悬停时的颜色
    shadowColor: const Color(0x14354677), // 阴影颜色
    focusColor: const Color(0xffFFF2E8), // 焦点状态下的颜色
    canvasColor: Colors.transparent, // 用于画布背景颜色
    dividerColor: const Color(0xFF0F0F0F).withValues(alpha: 0.1), // 分割// 分割线
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFFFFFFFF), // AppBar背景色
      titleTextStyle: TextStyle(
        color: Color(0xFF000000),
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ),
      iconTheme: IconThemeData(color: Color(0xFF8C6450)), // AppBar图标颜色
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFFFFFFFF),
      selectedItemColor: Color(0xFFC4A06C),
      unselectedItemColor: Color(0xFFCBC0AF),
    ),
    tabBarTheme: TabBarTheme(
      splashFactory: NoSplash.splashFactory, // 取消水波纹
      overlayColor: WidgetStateProperty.all(Colors.transparent), // 设置点击时的阴影颜色为透明
      dividerColor: Colors.transparent, //
    ),
    cardColor: Color(0xFFFFFFFF), // 卡片背景色
    cardTheme: const CardTheme(
      color: Color(0xFFFFFFFF), // 卡片背景色
    ),
    buttonTheme: const ButtonThemeData(
      splashColor: Colors.transparent, // 点击时的高亮效果
      highlightColor: Colors.transparent, // 长按时的扩散效果
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
        elevation: WidgetStateProperty.all(0), // 去除 ElevatedButton 阴影
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Color(0xFFFFFAF3), // TextField 背景色
      hintStyle: TextStyle(
        color: Color(0xFFCBC0AF), // 设置 hintText 的颜色
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: Color(0xFFDDB274),
        ),
      ),
    ),

    extensions: <ThemeExtension<dynamic>>[
      CustomColorTheme.gpLightA,
      CustomTextTheme.gpLightA,
    ],
  );

  ThemeData gpThemeDarkA = ThemeData.dark().copyWith(
    primaryColor: const Color(0xFFDDB274), // 主题色
    primaryColorLight: const Color(0xFFF6D199), // 主题色-浅色
    scaffoldBackgroundColor: const Color(0xff160D00), // Scaffold的默认背景颜色。
    hintColor: const Color(0xFF6B6254), // 用于提示文本或占位符文本的颜色
    splashColor: Colors.transparent, // 点击时的高亮效果
    highlightColor: Colors.transparent, // 长按时的扩散效果
    unselectedWidgetColor: const Color(0xffE9EDF3), // 用于未选中的复选框
    disabledColor: const Color(0xff9BA6B6), // 禁用状态下部件的颜色
    hoverColor: const Color(0xffE7E7E7), // 鼠标悬停时的颜色
    shadowColor: const Color(0x14354677), // 阴影颜色
    focusColor: const Color(0xffFFF2E8), // 焦点状态下的颜色
    canvasColor: Colors.transparent, // 用于画布背景颜色
    dividerColor: const Color(0xFF868989), // 分割线
    appBarTheme: AppBarTheme(
      backgroundColor: Color(0xff160D00), // AppBar背景色
      titleTextStyle: TextStyle(
        color: Color(0xFFFFFFFF),
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ), // AppBarTitle文字颜色
      iconTheme: IconThemeData(color: Color(0xFF8C6450)), // AppBar图标颜色
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xff160E03),
      selectedItemColor: Color(0xFFC4A06C),
      unselectedItemColor: Color(0xFF6B6254),
    ),
    tabBarTheme: TabBarTheme(
      splashFactory: NoSplash.splashFactory, // 取消水波纹
      overlayColor: WidgetStateProperty.all(Colors.transparent), // 设置点击时的阴影颜色为透明
      dividerColor: Colors.transparent, //
    ),
    cardColor: Color(0xFF1F1911), // 卡片背景色
    cardTheme: const CardTheme(
      color: Color(0xFF1F1911), // 卡片背景色
    ),
    buttonTheme: const ButtonThemeData(
      splashColor: Colors.transparent, // 点击时的高亮效果
      highlightColor: Colors.transparent, // 长按时的扩散效果
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
        elevation: WidgetStateProperty.all(0), // 去除 ElevatedButton 阴影
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Color(0xFF37342E), // TextField 背景色
      hintStyle: TextStyle(
        color: Color(0xFF6B6254), // 设置 hintText 的颜色
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: Color(0xFFDDB274),
        ),
      ),
    ),
    extensions: <ThemeExtension<dynamic>>[
      CustomColorTheme.gpDarkA,
      CustomTextTheme.gpDarkA,
    ],
  );
}
