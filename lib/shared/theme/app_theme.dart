// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
//
// import 'color_pallette.dart';
// import 'font_pallette.dart';
// import 'my_color_scheme.dart';
//
// class AppTheme {
//   static ThemeData get lightTheme {
//     return ThemeData(
//         useMaterial3: true,
//         brightness: Brightness.light,
//         primaryColor: ColorPalette.primaryColor,
//         dividerColor: Colors.transparent,
//         colorScheme: const ColorScheme.light(),
//         extensions: <ThemeExtension<MyColorScheme>>[
//           MyColorScheme.lightScheme,
//         ],
//         appBarTheme: AppBarTheme(
//             backgroundColor: ColorPalette.backgroundColor,
//             iconTheme: const CupertinoIconThemeData(),
//             surfaceTintColor: Colors.transparent),
//         scaffoldBackgroundColor: ColorPalette.backgroundColor,
//         textTheme: textLightTheme,
//         primaryTextTheme: textLightTheme,
//         fontFamily: FontPalette.themeFont,
//         textButtonTheme: TextButtonThemeData(
//           style: TextButton.styleFrom(foregroundColor: ColorPalette.primaryColor),
//         ),
//         textSelectionTheme: TextSelectionThemeData(cursorColor: ColorPalette.titleColor),
//         bottomNavigationBarTheme: BottomNavigationBarThemeData(
//           backgroundColor: ColorPalette.backgroundColor,
//         ),
//         bottomSheetTheme: BottomSheetThemeData(
//           backgroundColor: ColorPalette.backgroundColor,
//         ),
//         bottomAppBarTheme: BottomAppBarTheme(
//           color: ColorPalette.backgroundColor,
//         ),
//         iconButtonTheme: IconButtonThemeData(
//           style: IconButton.styleFrom(foregroundColor: ColorPalette.titleColor),
//         ),
//         dialogTheme: DialogTheme(
//           backgroundColor: ColorPalette.backgroundColor,
//           iconColor: ColorPalette.titleColor,
//         ),
//         iconTheme: IconThemeData(color: ColorPalette.titleColor),
//         elevatedButtonTheme: ElevatedButtonThemeData(
//           style: ElevatedButton.styleFrom(
//             backgroundColor: ColorPalette.primaryColor,
//             foregroundColor: Colors.white,
//             iconColor: Colors.white,
//           ),
//         ),
//         radioTheme: RadioThemeData(
//           fillColor: WidgetStateProperty.all(ColorPalette.primaryColor),
//           overlayColor: WidgetStateProperty.all(ColorPalette.primaryColor.withNewOpacity(0.4)),
//         ),
//         indicatorColor: ColorPalette.primaryColor,
//         chipTheme: ChipThemeData(
//           labelStyle: TextStyle(
//             color: ColorPalette.titleColorDark,
//             fontWeight: FontWeight.w400,
//           ),
//           selectedColor: ColorPalette.primaryColor,
//           surfaceTintColor: Colors.transparent,
//           shadowColor: Colors.transparent,
//           color: WidgetStateProperty.resolveWith(
//               (states) => states.contains(WidgetState.selected) ? ColorPalette.primaryColor : Colors.transparent),
//           shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10), side: BorderSide.none),
//         ),
//         inputDecorationTheme: InputDecorationTheme(
//           focusedBorder: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(5),
//             borderSide: BorderSide(color: ColorPalette.primaryColor),
//           ),
//           contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0),
//           enabledBorder: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(5),
//             borderSide: const BorderSide(color: Colors.transparent),
//           ),
//           border: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(5),
//             borderSide: BorderSide(color: ColorPalette.primaryColor.withAlpha(128)),
//           ),
//           fillColor: ColorPalette.textFieldBackgroundColor,
//           filled: true,
//         ),
//         filledButtonTheme: FilledButtonThemeData(
//           style: FilledButton.styleFrom(
//             backgroundColor: ColorPalette.primaryColor,
//             foregroundColor: Colors.white,
//             iconColor: Colors.white,
//           ),
//         ),
//         outlinedButtonTheme: OutlinedButtonThemeData(
//           style: OutlinedButton.styleFrom(
//             foregroundColor: ColorPalette.primaryColor,
//             side: BorderSide(color: ColorPalette.primaryColor),
//           ),
//         ));
//   }
//
//   static ThemeData get darkTheme {
//     return ThemeData(
//       useMaterial3: true,
//       brightness: Brightness.dark,
//       primaryColor: ColorPalette.primaryColorDark,
//       dividerColor: ColorPalette.dividerColorDark,
//       colorScheme: const ColorScheme.dark(),
//       extensions: <ThemeExtension<MyColorScheme>>[
//         MyColorScheme.darkScheme,
//       ],
//       scaffoldBackgroundColor: ColorPalette.backgroundColorDark,
//       textTheme: textDarkTheme,
//       primaryTextTheme: textDarkTheme,
//       textButtonTheme: TextButtonThemeData(
//         style: TextButton.styleFrom(
//           foregroundColor: ColorPalette.primaryColorDark,
//         ),
//       ),
//       textSelectionTheme: TextSelectionThemeData(cursorColor: ColorPalette.titleColorDark),
//       fontFamily: FontPalette.themeFont,
//       bottomNavigationBarTheme: BottomNavigationBarThemeData(
//         backgroundColor: ColorPalette.backgroundColorDark,
//       ),
//       bottomSheetTheme: BottomSheetThemeData(
//         backgroundColor: ColorPalette.cardColorDark,
//       ),
//       bottomAppBarTheme: BottomAppBarTheme(
//         color: ColorPalette.cardColorDark,
//       ),
//       iconButtonTheme: IconButtonThemeData(
//         style: IconButton.styleFrom(
//           foregroundColor: ColorPalette.titleColorDark,
//         ),
//       ),
//       dialogTheme: DialogTheme(
//         backgroundColor: ColorPalette.backgroundColorDark,
//         iconColor: ColorPalette.titleColorDark,
//       ),
//       iconTheme: IconThemeData(color: ColorPalette.titleColorDark),
//       elevatedButtonTheme: ElevatedButtonThemeData(
//         style: ElevatedButton.styleFrom(
//           backgroundColor: ColorPalette.primaryColorDark,
//         ),
//       ),
//       indicatorColor: ColorPalette.whiteColor,
//       appBarTheme: AppBarTheme(
//         backgroundColor: ColorPalette.backgroundColorDark,
//         iconTheme: const CupertinoIconThemeData(),
//       ),
//       chipTheme: ChipThemeData(
//         labelStyle: TextStyle(
//           color: ColorPalette.titleColorDark,
//         ),
//         selectedColor: ColorPalette.primaryColorDark,
//         shadowColor: Colors.transparent,
//         color: WidgetStateProperty.resolveWith(
//             (states) => states.contains(WidgetState.selected) ? ColorPalette.primaryColor : Colors.transparent),
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10), side: BorderSide.none),
//       ),
//       inputDecorationTheme: InputDecorationTheme(
//         focusedBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(5),
//           borderSide: BorderSide(color: ColorPalette.primaryColor),
//         ),
//         contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0),
//         enabledBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(5),
//           borderSide: const BorderSide(color: Colors.transparent),
//         ),
//         fillColor: ColorPalette.textFieldBackgroundColorDark,
//         filled: true,
//         border: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(5),
//           borderSide: BorderSide(color: ColorPalette.primaryColor.withAlpha(128)),
//         ),
//       ),
//       filledButtonTheme: FilledButtonThemeData(
//         style: FilledButton.styleFrom(
//           backgroundColor: ColorPalette.primaryColorDark,
//         ),
//       ),
//       outlinedButtonTheme: OutlinedButtonThemeData(
//         style: OutlinedButton.styleFrom(
//           foregroundColor: ColorPalette.primaryColorDark,
//           side: BorderSide(color: ColorPalette.primaryColorDark),
//         ),
//       ),
//     );
//   }
//
//   static TextTheme get textDarkTheme {
//     return Typography.englishLike2018.apply(
//       fontSizeFactor: 0.8,
//       bodyColor: ColorPalette.titleColorDark,
//       fontFamily: FontPalette.themeFont,
//     );
//   }
//
//   static TextTheme get textLightTheme {
//     return Typography.englishLike2018.apply(
//       fontSizeFactor: 0.8,
//       bodyColor: ColorPalette.titleColor,
//       fontFamily: FontPalette.themeFont,
//     );
//   }
// }
