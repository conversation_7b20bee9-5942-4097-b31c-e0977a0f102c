import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

enum CommonButtonStyle {
  primary, // 主按钮样式 Primary button
  stockRed, // 股票红样式 Stock red
  stockGreen, // 股票绿样式 Stock green
  outlined, // 线框按钮样式 Outlined button
}

class CommonButton extends StatefulWidget {
  final String title; // 按钮标题 Button title
  final CommonButtonStyle style; // 样式枚举 Button style
  final VoidCallback? onPressed; // 点击事件 Click callback
  final Widget? prefix; // 前缀图标 Prefix icon
  final Widget? child; // 自定义内容（优先） Custom child
  final bool showLoading; // 是否显示加载圈 Show loading spinner
  final bool enable; // 启用状态 Enable state
  final int delayMilliseconds; // 点击防抖延迟 Click delay

  final double? width;
  final double height;
  final double radius;
  final EdgeInsets? titlePadding;
  final String? bgImgPath;

  final Color? textColor;
  final Color? backgroundColor;
  final double fontSize;
  final FontWeight? fontWeight;
  final Color? borderColor;
  final double prefixLeftPadding;

  CommonButton({
    super.key,
    required this.title,
    this.style = CommonButtonStyle.primary,
    this.onPressed,
    this.prefix,
    this.child,
    this.showLoading = false,
    this.enable = true,
    this.delayMilliseconds = 2000,
    this.width,
    this.titlePadding,
    this.bgImgPath,
    this.textColor,
    this.backgroundColor,
    this.fontWeight,
    this.borderColor,
    double? height,
    double? fontSize,
    double? radius,
    double? prefixLeftPadding,
  })  : height = height ?? 42.gw,
        fontSize = fontSize ?? 18.gsp,
        radius = radius ?? 10.gr,
        prefixLeftPadding = prefixLeftPadding ?? 15.0;

  @override
  State<CommonButton> createState() => _CommonButtonState();
}

class _CommonButtonState extends State<CommonButton> {
  bool _isClickable = true;

  @override
  void initState() {
    super.initState();
  }

  void _handleOnPressed() async {
    if (!_isClickable || widget.onPressed == null) return;

    widget.onPressed!();
    if (widget.delayMilliseconds > 0) {
      setState(() => _isClickable = false);
      await Future.delayed(Duration(milliseconds: widget.delayMilliseconds));
      if (mounted) setState(() => _isClickable = true);
    }
  }

  /// 构建实际内容（支持 child, loading, title + prefix）
  Widget _buildContent(Color resolvedTextColor) {
    if (widget.showLoading) {
      return SizedBox(
        width: 18,
        height: 18,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: resolvedTextColor,
        ),
      );
    }
    if (widget.child != null) return widget.child!;
    return Stack(
      alignment: Alignment.center,
      children: [
        if (widget.prefix != null)
          Positioned(
            left: widget.prefixLeftPadding,
            child: widget.prefix!,
          ),
        Center(
          child: Text(
            widget.title,
            style: TextStyle(
              color: resolvedTextColor,
              fontSize: widget.fontSize,
              fontWeight: widget.fontWeight ?? FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  /// 样式处理：背景、边框、文字色
  ({BoxDecoration decoration, Color textColor}) _resolveStyle() {
    final theme = context.theme;
    final colorTheme = context.colorTheme;
    final baseDecoration = BoxDecoration(
      borderRadius: BorderRadius.circular(widget.radius),
      boxShadow: [
        BoxShadow(
          color: context.theme.shadowColor,
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    );

    switch (widget.style) {
      case CommonButtonStyle.primary:
        return (
          decoration: baseDecoration.copyWith(
              gradient: LinearGradient(
            colors: [theme.primaryColorLight, theme.primaryColor],
          )),
          textColor: widget.textColor ?? Colors.white
        );

      case CommonButtonStyle.stockRed:
        return (
          decoration: baseDecoration.copyWith(color: colorTheme.stockRed),
          textColor: widget.textColor ?? colorTheme.buttonPrimary
        );

      case CommonButtonStyle.stockGreen:
        return (
          decoration: baseDecoration.copyWith(color: colorTheme.stockGreen),
          textColor: widget.textColor ?? colorTheme.buttonPrimary
        );
      case CommonButtonStyle.outlined:
        return (
          decoration: baseDecoration.copyWith(
            color: Colors.transparent,
            border: Border.all(color: widget.borderColor ?? colorTheme.buttonPrimary),
          ),
          textColor: widget.textColor ?? theme.primaryColor
        );
    }
  }

  BoxDecoration _buildImageDecoration() {
    return BoxDecoration(
      image: DecorationImage(
        image: AssetImage(widget.bgImgPath!),
        fit: BoxFit.cover,
      ),
      borderRadius: BorderRadius.circular(widget.radius),
    );
  }

  @override
  Widget build(BuildContext context) {
    final resolved = _resolveStyle();
    final disable = !widget.enable || widget.showLoading || !_isClickable;
    final textColor = disable ? resolved.textColor.withNewOpacity(0.6) : resolved.textColor;
    return GestureDetector(
      onTap: disable ? null : _handleOnPressed,
      child: Container(
        width: widget.width ?? double.infinity,
        height: widget.height,
        decoration: widget.bgImgPath != null ? _buildImageDecoration() : resolved.decoration.copyWith(),
        padding: widget.titlePadding ?? const EdgeInsets.symmetric(horizontal: 16),
        alignment: Alignment.center,
        // child: _buildContent(resolved.textColor.withNewOpacity(disable ? 0.6 : null)),
        child: _buildContent(textColor),
      ),
    );
  }
}
