import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../../theme/color_pallette.dart';

class ShimmerWidget extends StatelessWidget {
  const ShimmerWidget({
    super.key,
    this.child,
    this.isLoading = true,
    this.isError = false,
    this.shimmerChild,
    this.width,
    this.height,
    this.color = Colors.white,
    this.baseColor,
    this.highlightColor,
    this.radius = 4,
  });

  final Widget? child;
  final Widget? shimmerChild;
  final bool isLoading, isError;
  final double? width, height;
  final double radius;
  final Color? color, baseColor, highlightColor;

  @override
  Widget build(BuildContext context) {
    if (isError) {
      return const SizedBox();
    }
    if (isLoading) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: Shimmer.fromColors(
          baseColor: baseColor ?? (Theme.of(context).brightness == Brightness.dark ? Colors.grey[900]! : Colors.white),
          highlightColor: highlightColor ??
              (Theme.of(context).brightness == Brightness.dark ? Colors.grey[800]! : Colors.grey[200]!),
          child: height != null
              ? Container(
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(radius),
                    boxShadow: [
                      BoxShadow(
                        color: ColorPalette.primaryColor,
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  width: width ?? double.infinity,
                  height: height ?? double.infinity,
                )
              : shimmerChild ?? child ?? const SizedBox(),
        ),
      );
    } else if (!isLoading) {
      return child ?? const SizedBox();
    } else {
      return const SizedBox();
    }
  }
}
