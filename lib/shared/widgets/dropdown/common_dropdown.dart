import 'package:dropdown_search/dropdown_search.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';

import '../../models/dropdown/dropdown_value.dart';
import '../../theme/color_pallette.dart';
import '../../theme/font_pallette.dart';

class CommonDropdown<T> extends StatelessWidget {
  final String hintText;
  final List<DropDownValue> dropDownValue;
  final DropDownValue? selectedItem;
  final Function onChanged;
  final bool showSearchBox;
  final bool isVisibleClearButton;
  final bool isEnabled;
  final Color? fillColor;
  final bool isSuperDense;
  final FormFieldValidator<DropDownValue>? validator;
  final double? height;
  final Widget Function(BuildContext, String)? emptyBuilder;
  final Widget Function(BuildContext, DropDownValue, bool, bool)? itemBuilder;
  final double? borderRadius;
  final TextStyle? textStyle;
  final bool hideSuffixIcon;
  final Widget Function(BuildContext, DropDownValue?, TextStyle?)? selectedItemBuilder;
  final String? currency;
  const CommonDropdown({
    super.key,
    required this.hintText,
    required this.dropDownValue,
    this.selectedItem,
    required this.onChanged,
    this.showSearchBox = true,
    this.isSuperDense = false,
    this.validator,
    this.isEnabled = true,
    this.isVisibleClearButton = false,
    this.fillColor,
    this.height,
    this.emptyBuilder,
    this.itemBuilder,
    this.borderRadius,
    this.textStyle,
    this.selectedItemBuilder,
    this.hideSuffixIcon = false,
    this.currency,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height ?? 40.gh,
      child: DropdownSearch<DropDownValue>(
        suffixProps: hideSuffixIcon || !isEnabled
            ? DropdownSuffixProps(
                dropdownButtonProps:
                    DropdownButtonProps(iconOpened: const SizedBox.shrink(), iconClosed: const SizedBox.shrink()))
            : DropdownSuffixProps(
                dropdownButtonProps: DropdownButtonProps(
                iconOpened: SvgPicture.asset(Assets.arrowUpIcon,
                    width: 10.gw,
                    height: 8.gh,
                    colorFilter: ColorFilter.mode(context.colorTheme.textPrimary, BlendMode.srcIn)),
                iconClosed: SvgPicture.asset(Assets.downArrow2Icon,
                    width: 10.gw,
                    height: 8.gh,
                    colorFilter: ColorFilter.mode(context.colorTheme.textPrimary, BlendMode.srcIn)),
              )),
        items: (filter, loadProps) => dropDownValue,
        selectedItem: selectedItem,
        enabled: isEnabled,
        validator: validator,
        // dropdownBuilder: (context, selectedItem) => Text(
        //   selectedItem?.value ?? '',
        //   style: textStyle ??
        //       FontPalette.normal14.copyWith(
        //         color: isEnabled ? context.colorTheme.regular : context.colorTheme.regular,
        //       ),
        // ),
        dropdownBuilder: (context, selectedItem) {
          if (selectedItem == null) {
            return SizedBox.shrink();
          }

          // Use custom builder if provided, otherwise fallback to default
          if (selectedItemBuilder != null) {
            final defaultStyle = textStyle ??
                FontPalette.normal14.copyWith(
                  color: isEnabled ? context.colorTheme.textRegular : context.colorTheme.textRegular,
                );
            return selectedItemBuilder!(context, selectedItem, defaultStyle);
          }

          // Default builder
          return Text(
            '${selectedItem.value ?? ''} ${currency ?? ''}',
            style: textStyle ??
                FontPalette.normal14.copyWith(
                  color: isEnabled ? context.colorTheme.textRegular : context.colorTheme.textRegular,
                ),
          );
        },
        onChanged: (value) {
          onChanged(value);
        },
        onBeforeChange: (prevItem, nextItem) async {
          return true;
        },
        compareFn: (item1, item2) => item1.code == item2.code, // Add compareFn
        popupProps: PopupProps.menu(
          fit: FlexFit.loose,
          showSearchBox: showSearchBox,
          menuProps: MenuProps(
            backgroundColor: context.theme.cardColor,
            elevation: 2.0,
          ),
          emptyBuilder: emptyBuilder ?? customPopupEmptyBuilder,
          itemBuilder: (context, item, isDisabled, isSelected) => itemBuilder != null
              ? itemBuilder!(context, item, isDisabled, isSelected)
              : customPopupItemBuilder(context, item, isSelected),
          searchFieldProps: TextFieldProps(
              cursorColor: ColorPalette.textColor,
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                hintText: 'search'.tr(),
                hintStyle: FontPalette.normal14.copyWith(color: context.colorTheme.textRegular),
                labelStyle: FontPalette.normal14.copyWith(color: context.colorTheme.textRegular),
                errorStyle: const TextStyle(height: 0.1),
                errorText: null,
                isDense: true,
                prefixIcon: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Icon(
                    Icons.search,
                    color: ColorPalette.textColor,
                    size: 15,
                  ),
                ),
                prefixIconConstraints: const BoxConstraints(maxHeight: 20),
                enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: const Color(0xffB3B3BF).withNewOpacity(0.5)),
                    borderRadius: const BorderRadius.all(
                      Radius.circular(10),
                    )),
                focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: const Color(0xffB3B3BF).withNewOpacity(0.5)),
                    borderRadius: const BorderRadius.all(
                      Radius.circular(10),
                    )),
              )),
          containerBuilder: (ctx, popupWidget) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Flexible(
                  child: Container(
                    child: popupWidget,
                  ),
                ),
              ],
            );
          },
        ),
        decoratorProps: DropDownDecoratorProps(
          decoration: InputDecoration(
            floatingLabelBehavior: FloatingLabelBehavior.always,
            // labelText: widget.isHint ? null : widget.labelText,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 12.gr),
              borderSide: BorderSide(color: Colors.transparent),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 12.gr),
              borderSide:
                  BorderSide(color: isSuperDense ? Colors.transparent : ColorPalette.primaryColor.withAlpha(128)),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 12.gr),
              borderSide: BorderSide(color: ColorPalette.redColor.withAlpha(128)),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 12.gr),
              borderSide: BorderSide(color: ColorPalette.redColor.withAlpha(128)),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 12.gr),
              borderSide: BorderSide(color: Colors.transparent),
            ),
            labelStyle: FontPalette.normal14.copyWith(
              color: context.colorTheme.textRegular,
            ),
            fillColor: fillColor ?? context.theme.scaffoldBackgroundColor,
            filled: true,
            hintText: selectedItem == null ? hintText : null,
            hintStyle: FontPalette.normal14.copyWith(color: context.colorTheme.textRegular),
            isDense: true,
            errorText: null,
            errorStyle: TextStyle(color: ColorPalette.redColor, fontSize: 14.gsp, height: 1),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.gw,
              vertical: isSuperDense ? 0 : 8.gh,
            ),
          ),
        ),
      ),
    );
  }

  Widget customPopupItemBuilder(
    BuildContext context,
    DropDownValue? item,
    bool isSelected,
  ) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.0.gw),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.0.gh),
        child: Row(
          children: [
            Expanded(
              child: Text(
                item?.value ?? '',
                style: FontPalette.normal14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget customPopupEmptyBuilder(BuildContext context, String search) {
    return SizedBox(
      height: 100.gh,
      child: Center(
        child: Text(
          'no_data_available'.tr(),
          style: FontPalette.normal14.copyWith(color: ColorPalette.textLabelColor),
        ),
      ),
    );
  }
}
