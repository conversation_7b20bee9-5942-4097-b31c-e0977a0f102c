import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class ShadowBox extends StatelessWidget {
  const ShadowBox({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(4.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.1), // Light shadow
            blurRadius: 8, // Soft blur effect
            spreadRadius: 2,
            offset: Offset(0, 4), // Downward shadow
          ),
        ],
      ),
      child: child,
    );
  }
}
