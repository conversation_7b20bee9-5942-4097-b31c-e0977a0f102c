import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_stock_app/core/utils/keyboard_config.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

/// Custom TextInputFormatter that replaces commas with periods
/// to handle decimal input in the different locales.
class CommaReplacementFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Replace all commas with periods
    final String newText = newValue.text.replaceAll(',', '.');

    // If the text didn't change, return the original value
    if (newText == newValue.text) {
      return newValue;
    }

    // Return new value with replaced text and adjusted selection
    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}

/// A customizable numeric input field with increment/decrement buttons.
///
/// This widget provides:
/// - Numeric input with decimal support
/// - Increment/decrement buttons for value adjustment
/// - Locale-aware decimal input (handles both comma and period separators)
/// - Automatic text selection on focus
/// - Custom keyboard configuration
/// - Focus management
///
/// Example usage:
/// ```dart
/// CounterTextfield(
///   controller: textController,
///   onIncrementPressed: () => handleIncrement(),
///   onDecrementPressed: () => handleDecrement(),
///   onFocusChanged: () => handleFocusChange(),
/// )
/// ```
class CounterTextfield extends StatefulWidget {
  /// Creates a CounterTextfield.
  ///
  /// [controller] manages the text input value
  ///
  /// [onIncrementPressed] callback for increment button press
  ///
  /// [onDecrementPressed] callback for decrement button press
  ///
  /// [focusNode] optional external focus node for controlling focus
  ///
  /// [onFocusChanged] callback triggered when focus changes
  const CounterTextfield({
    super.key,
    required this.onIncrementPressed,
    required this.onDecrementPressed,
    required this.controller,
    this.focusNode,
    this.onFocusChanged,
  });

  final VoidCallback? onIncrementPressed;
  final VoidCallback? onDecrementPressed;
  final TextEditingController controller;
  final VoidCallback? onFocusChanged;
  final FocusNode? focusNode;

  @override
  State<CounterTextfield> createState() => _CounterTextfieldState();
}

class _CounterTextfieldState extends State<CounterTextfield> {
  static const double _buttonSize = 20.0;
  static const double _buttonRadius = 4.0;
  static const double _iconSize = 16.0;
  static const double _horizontalSpacing = 8.0;

  final FocusNode _textFieldFocusNode = FocusNode();

  @override
  void dispose() {
    _textFieldFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 35.gh,
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(5.gr),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        spacing: _horizontalSpacing,
        children: [
          _buildControlButton(
            icon: Icons.remove,
            onPressed: widget.onDecrementPressed,
          ),
          Expanded(child: _buildTextField()),
          _buildControlButton(
            icon: Icons.add,
            onPressed: widget.onIncrementPressed,
          ),
        ],
      ),
    );
  }

  /// Builds the increment/decrement button
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        height: _buttonSize.gh,
        width: _buttonSize.gw,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_buttonRadius.gr),
          color: context.theme.cardColor,
        ),
        child: Center(
          child: Icon(
            icon,
            size: _iconSize.gr,
            color: onPressed == null ? context.theme.dividerColor : context.colorTheme.textPrimary,
          ),
        ),
      ),
    );
  }

  /// Builds the text input field
  Widget _buildTextField() {
    return Focus(
      focusNode: widget.focusNode,
      onFocusChange: (hasFocus) {
        if (!hasFocus) {
          widget.onFocusChanged?.call();
        }
      },
      child: KeyboardActions(
        config: KeyboardConfig.buildConfig(context, _textFieldFocusNode),
        child: TextField(
          focusNode: _textFieldFocusNode,
          controller: widget.controller,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[\d\.,]')),
            CommaReplacementFormatter(),
          ],
          onTap: _handleTextFieldTap,
          decoration: InputDecoration(
            border: InputBorder.none,
            focusedBorder: InputBorder.none,
            hintStyle: FontPalette.normal13,
            isDense: true,
            contentPadding: EdgeInsets.symmetric(
              horizontal: _horizontalSpacing.gw,
              vertical: _horizontalSpacing.gh,
            ),
          ),
          textAlign: TextAlign.center,
          style: FontPalette.normal13.copyWith(
            color: context.colorTheme.textRegular,
          ),
        ),
      ),
    );
  }

  void _handleTextFieldTap() {
    widget.controller.selection = TextSelection(
      baseOffset: 0,
      extentOffset: widget.controller.text.length,
    );
  }
}
