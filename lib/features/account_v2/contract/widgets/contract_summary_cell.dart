import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/contract_summary_page_entity.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/data_field.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../core/utils/utils.dart';
import '../../../../shared/theme/font_pallette.dart';
import '../../../../shared/widgets/symbol/symbol_chip.dart';

class ContractSummaryCell extends StatelessWidget {
  final ContractSummaryPageRecord model;
  const ContractSummaryCell({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5.gr),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          spacing: 3.gh,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  spacing: 5.gh,
                  children: [
                    SymbolChip(name: model.marketType, chipColor: context.theme.primaryColor),
                    SymbolChip(name: model.currency.toUpperCase(), chipColor: context.colorTheme.textRegular),
                    SizedBox(
                      width: 220.gr,
                      child: Text(
                        getContractLabelByContractSummaryPageRecord(model),
                        style: FontPalette.semiBold12.copyWith(
                          overflow: TextOverflow.ellipsis,
                        ),
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
                Icon(Icons.arrow_forward_ios, size: 16.gr, color: context.theme.primaryColor),
              ],
            ),
            5.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: 10.gh,
              children: [
                Expanded(child: DataField(label: 'availableBalance'.tr(), value: model.useAmount)),
                Expanded(child: DataField(label: 'frozenAmount'.tr(), value: model.freezePower)),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: 10.gh,
              children: [
                Expanded(
                    child: DataField(
                  label: 'unrealizedPnl'.tr(),
                  value: model.todayWinAmount,
                  color: model.todayWinAmount.getValueColor(context),
                )),
                Expanded(
                    child: DataField(
                  label: '',
                  value: model.todayWinRate,
                  suffix: '%',
                )),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class ContractSummaryShimmerCell extends StatelessWidget {
  const ContractSummaryShimmerCell({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.fromLTRB(16.gw, 0, 16.gw, 10.gw),
      padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 16.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top row: chips + title
            Row(
              children: [
                _box(40, 15, borderRadius: 2), // marketType chip
                SizedBox(width: 5.gw),
                _box(100, 15, borderRadius: 2), // currency chip
              ],
            ),
            SizedBox(height: 9.gh),
            // Second row: available & frozen
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(child: _dataColumnSkeleton()),
                SizedBox(width: 10.gh),
                Expanded(child: _dataColumnSkeleton()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _dataColumnSkeleton({double width = 60}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _box(width, 12),
            _box(35, 12),
          ],
        ),
        SizedBox(height: 3.gw),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _box(width, 12),
            _box(35, 12),
          ],
        ),
      ],
    );
  }

  Widget _box(double width, double height, {double borderRadius = 4}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}
