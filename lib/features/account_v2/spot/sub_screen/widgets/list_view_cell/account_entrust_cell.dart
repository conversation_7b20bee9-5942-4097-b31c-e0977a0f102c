import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:shimmer/shimmer.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

final _flexValues = [6, 5, 5, 5, 3];

/// 委托明细Cell
class AccountEntrustCell extends StatelessWidget {
  const AccountEntrustCell({
    super.key,
    required this.data,
    required this.onTap,
    required this.onTapCancelBtn,
    this.isLast = false,
  });

  final FTradeAcctOrderRecords data;
  final VoidCallback onTap;
  final VoidCallback onTapCancelBtn;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    final entrustStatus = EntrustStatus.fromValueByValue(data.status);
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: isLast
                ? BorderRadius.only(
                    bottomLeft: Radius.circular(10.gr),
                    bottomRight: Radius.circular(10.gr),
                  )
                : null),
        padding: EdgeInsets.fromLTRB(6.gw, 8.gw, 0, 8.gw),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Name and Symbol Column
            Expanded(
              flex: _flexValues[0],
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 5.gh,
                children: [
                  Text(
                    data.symbolName,
                    style: FontPalette.semiBold14,
                  ),
                  Row(
                    spacing: 5.gh,
                    children: [
                      SymbolChip(name: data.market.substring(0, 2), chipColor: context.theme.primaryColor),
                      Text(
                        data.symbol,
                        style: FontPalette.semiBold12.copyWith(
                          color: context.colorTheme.textRegular,
                          fontFamily: 'Akzidenz-Grotesk',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            Expanded(
                flex: _flexValues[1],
                child: AnimatedFlipCounter(
                  fractionDigits: 2,
                  decimalSeparator: '.',
                  thousandSeparator: ',',
                  textStyle: FontPalette.bold14.copyWith(
                    color: ColorPalette.primaryColor,
                    fontFamily: 'Akzidenz-Grotesk',
                  ),
                  value: data.tradePrice,
                )),

            Expanded(
              flex: _flexValues[2],
              child: Column(
                children: [
                  Text(
                    data.dealNum.toNumeric,
                    textAlign: TextAlign.center,
                    style: FontPalette.semiBold14
                        .copyWith(fontFamily: 'Akzidenz-Grotesk', color: context.colorTheme.textRegular),
                  ),
                  Text(
                    data.tradeNum.toNumeric,
                    textAlign: TextAlign.center,
                    style: FontPalette.semiBold14
                        .copyWith(fontFamily: 'Akzidenz-Grotesk', color: context.colorTheme.textRegular),
                  ),
                ],
              ),
            ),

            Expanded(
              flex: _flexValues[3],
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    '${TradeDirection.fromValue(data.direction).translationKey.tr()}${TradeType.fromValue(data.tradeType).translationKey.tr()}',
                    textAlign: TextAlign.center,
                    style: FontPalette.normal12.copyWith(
                        color:
                            TradeDirection.getColor(context, tradeDirection: TradeDirection.fromValue(data.direction))),
                  ),
                  Text(
                    entrustStatus.label.tr(),
                    textAlign: TextAlign.right,
                    style: FontPalette.semiBold10.copyWith(
                      color: entrustStatus.color,
                      // fontFamily: 'Akzidenz-Grotesk',
                      fontSize: 10.gh,
                    ),
                  ),
                ],
              ),
            ),
            8.horizontalSpace,
            Expanded(
              flex: _flexValues[4],
              child: InkWell(
                onTap: onTapCancelBtn,
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: Text(
                    'revoke'.tr(),
                    textAlign: TextAlign.right,
                    style: FontPalette.bold12.copyWith(
                      color: context.colorTheme.stockRed,
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class AccountEntrustShimmerCell extends StatelessWidget {
  const AccountEntrustShimmerCell({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.gw),
      padding: EdgeInsets.fromLTRB(6.gw, 8.gw, 0.gw, 8.gw),
      color: context.theme.cardColor,
      child: Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 左侧：名称 + 代码
            Expanded(
              flex: _flexValues[0],
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _box(60, 10),
                  const SizedBox(height: 1),
                  _box(36, 10),
                ],
              ),
            ),

            // 中间：成交价格

            Expanded(
              flex: _flexValues[1],
              child: Align(
                alignment: Alignment.center,
                child: _box(45, 10),
              ),
            ),

            // 中间：委托数量 + 成交数量
            Expanded(
              flex: _flexValues[2],
              child: Column(
                children: [
                  _box(40, 10),
                  const SizedBox(height: 1),
                  _box(40, 10),
                ],
              ),
            ),

            // 中间：买入卖出 + 状态
            Expanded(
              flex: _flexValues[3],
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _box(60, 10),
                  const SizedBox(height: 1),
                  _box(40, 10),
                ],
              ),
            ),

            // 右侧：撤单按钮
            Expanded(
              flex: _flexValues[4],
              child: Align(
                alignment: Alignment.center,
                child: _box(28, 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _box(double width, double height, {double borderRadius = 2}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}
