import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/convert_rate/widgets/currency_calculator_keyboard.dart';
import 'package:gp_stock_app/features/main/widgets/draggable_float_widget.dart';

import '../../../shared/constants/assets.dart';
import '../../../shared/constants/enums.dart';
import '../logic/convert_rate/convert_rate_cubit.dart';
import '../logic/convert_rate/convert_rate_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ConvertRateScreen extends StatefulWidget {
  final bool autoFocusOnLoad;

  const ConvertRateScreen({
    super.key,
    this.autoFocusOnLoad = true,
  });

  @override
  State<ConvertRateScreen> createState() => _ConvertRateScreenState();
}

class _ConvertRateScreenState extends State<ConvertRateScreen> {
  final TextEditingController _amountController = TextEditingController(text: "1");
  final FocusNode _amountFocusNode = FocusNode();
  bool _showKeyboard = false;
  bool _isInitialLoad = true;

  @override
  void initState() {
    super.initState();

    // Fetch rates when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ConvertRateCubit>().fetchConvertRate();

      // Auto-focus and show keyboard if enabled
      if (widget.autoFocusOnLoad) {
        setState(() {
          _showKeyboard = true;
        });
        _amountFocusNode.requestFocus();
      }
    });

    // Add listener to focus node to toggle keyboard
    _amountFocusNode.addListener(() {
      if (_amountFocusNode.hasFocus && !_showKeyboard) {
        setState(() {
          _showKeyboard = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _amountFocusNode.removeListener(() {});
    _amountController.dispose();
    _amountFocusNode.dispose();

    // Reset floating widget position when screen is closed
    _updateFloatingWidgetPosition(false);

    super.dispose();
  }

  // Centralized floating widget position management
  void _updateFloatingWidgetPosition(bool shouldShowKeyboard) {
    if (shouldShowKeyboard) {
      FloatingWidgetManager().updatePosition(FloatingPosition.centerRight);
    } else {
      FloatingWidgetManager().updatePosition(FloatingPosition.bottomRight);
    }
  }

  void _toggleKeyboard() {
    setState(() {
      _showKeyboard = !_showKeyboard;

      if (_showKeyboard) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _amountFocusNode.requestFocus();
          // Force TextField repaint by updating selection twice
          final text = _amountController.text;
          _amountController.value = TextEditingValue(
            text: text,
            selection: TextSelection.collapsed(offset: 0), // Temporary offset
          );
          _amountController.value = TextEditingValue(
            text: text,
            selection: TextSelection.collapsed(offset: text.length), // Final position
          );
          // Re-request focus to ensure cursor visibility
          _amountFocusNode.requestFocus();
        });
      } else {
        _amountFocusNode.unfocus();
        // When closing keyboard, finish editing
        context.read<ConvertRateCubit>().updateAmount(
              _amountController.text,
              isEditing: false,
              recalculate: true,
            );
      }

      // Update floating widget position based on keyboard state
      _updateFloatingWidgetPosition(_showKeyboard);
    });
  }

  void _selectCurrency(String currency) {
    if (currency == context.read<ConvertRateCubit>().state.selectedCurrency) {
      // If already selected, just toggle keyboard
      _toggleKeyboard();
      return;
    }

    // Otherwise switch currency
    context.read<ConvertRateCubit>().updateSelectedCurrency(currency);

    setState(() {
      _showKeyboard = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _amountFocusNode.requestFocus();
        // Force TextField repaint by updating selection twice
        final text = _amountController.text;
        _amountController.value = TextEditingValue(
          text: text,
          selection: TextSelection.collapsed(offset: 0), // Temporary offset
        );
        _amountController.value = TextEditingValue(
          text: text,
          selection: TextSelection.collapsed(offset: text.length), // Final position
        );
        // Re-request focus to ensure cursor visibility
        _amountFocusNode.requestFocus();
      });
    });

    // Update floating widget position
    _updateFloatingWidgetPosition(true);
  }

  // Format amount based on whether it's a whole number
  String _formatAmount(double? value) {
    if (value == null) return "0";

    if (value == value.toInt()) {
      return value.toInt().toString();
    } else {
      return value.toStringAsFixed(4);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: context.colorTheme.textPrimary,
            size: 20.gw,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'exchange'.tr(),
          style: TextStyle(
            color: context.colorTheme.textPrimary,
            fontSize: 18.gsp,
          ),
        ),
        actions: [
          BlocBuilder<ConvertRateCubit, ConvertRateState>(
            buildWhen: (previous, current) => previous.status != current.status,
            builder: (context, state) {
              final isLoading = state.status == DataStatus.loading;
              return isLoading
                  ? Padding(
                      padding: EdgeInsets.only(right: 16.gw),
                      child: SizedBox(
                        width: 20.gw,
                        height: 20.gh,
                        child: CircularProgressIndicator.adaptive(
                          strokeWidth: 2,
                        ),
                      ),
                    )
                  : IconButton(
                      icon: Icon(
                        Icons.refresh,
                        color: context.colorTheme.textPrimary,
                        size: 20.gw,
                      ),
                      onPressed: () => context.read<ConvertRateCubit>().fetchConvertRate(),
                    );
            },
          ),
        ],
      ),
      body: BlocConsumer<ConvertRateCubit, ConvertRateState>(
        listenWhen: (previous, current) {
          // Only listen for relevant state changes
          return previous.selectedCurrency != current.selectedCurrency ||
              previous.customAmount != current.customAmount ||
              (previous.status != current.status && current.status == DataStatus.success);
        },
        listener: (context, state) {
          // Only update the text controller if:
          // 1. We're not currently editing, or
          // 2. It's for a currency switch, or
          // 3. It's during initial load
          if (!state.isEditingAmount ||
              context.read<ConvertRateCubit>().state.selectedCurrency != state.selectedCurrency ||
              _isInitialLoad) {
            if (_isInitialLoad && state.status == DataStatus.success) {
              _isInitialLoad = false;

              // If keyboard is shown during initial load, update floating widget position
              if (_showKeyboard) {
                FloatingWidgetManager().updatePosition(FloatingPosition.centerRight);
              } else {
                FloatingWidgetManager().updatePosition(FloatingPosition.bottomRight);
              }
            }

            // Don't update while user is actively editing
            if (_amountController.text != state.customAmount) {
              // Set the controller text from state and move cursor to end
              _amountController.value = TextEditingValue(
                text: state.customAmount,
                selection: TextSelection.collapsed(offset: state.customAmount.length),
              );
              // Force TextField repaint by updating selection twice
              final text = _amountController.text;
              _amountController.value = TextEditingValue(
                text: text,
                selection: TextSelection.collapsed(offset: 0), // Temporary offset
              );
              _amountController.value = TextEditingValue(
                text: text,
                selection: TextSelection.collapsed(offset: text.length), // Final position
              );
              // Ensure focus if keyboard is shown
              if (_showKeyboard) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _amountFocusNode.requestFocus();
                });
              }
            }
          }
        },
        builder: (context, state) {
          if (state.status == DataStatus.loading && state.rates == null) {
            return _buildLoadingState();
          }

          if (state.status == DataStatus.failed) {
            return Center(child: Text(state.error ?? 'errorMsg'.tr()));
          }

          final rates = state.rates;
          if (rates == null || rates.isEmpty) {
            return Center(child: Text('no_data_available'.tr()));
          }

          return GestureDetector(
            onTap: () {
              if (_showKeyboard) {
                // This will handle the FloatingWidgetManager position update
                _toggleKeyboard();
              }
            },
            behavior: HitTestBehavior.translucent,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(16.gw),
                    child: Column(
                      children: [
                        _buildCurrencyConverterCard(context, state),
                      ],
                    ),
                  ),
                ),
                if (_showKeyboard)
                  GestureDetector(
                    onTap: () {},
                    behavior: HitTestBehavior.opaque,
                    child: CurrencyCalculatorKeyboard(
                      controller: _amountController,
                      onValueChanged: (value) {
                        // Update the editing state with recalculate: true
                        context.read<ConvertRateCubit>().updateAmount(value, isEditing: true, recalculate: true);
                      },
                      onCalculate: (value) {
                        // Calculation is complete
                        context.read<ConvertRateCubit>().calculationComplete(value);
                      },
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: context.colorTheme.textPrimary,
          ),
          16.verticalSpace,
          Text(
            'loading'.tr(),
            style: context.textTheme.regular.fs14.w400,
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencyConverterCard(BuildContext context, ConvertRateState state) {
    return Container(
      padding: EdgeInsets.all(20.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(16.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildCurrencyRow(
            context,
            'CNY',
            _formatAmount(state.convertedCNY),
            state.selectedCurrency == 'CNY',
          ),
          8.verticalSpace,
          _buildCurrencyRow(
            context,
            'USD',
            _formatAmount(state.convertedUSD),
            state.selectedCurrency == 'USD',
          ),
          8.verticalSpace,
          _buildCurrencyRow(
            context,
            'HKD',
            _formatAmount(state.convertedHKD),
            state.selectedCurrency == 'HKD',
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencyRow(BuildContext context, String currency, String amount, bool isSelected) {
    return InkWell(
      onTap: () {
        _selectCurrency(currency);
      },
      borderRadius: BorderRadius.circular(8.gr),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.gh, horizontal: 8.gw),
        decoration: BoxDecoration(
          color: isSelected ? context.colorTheme.textPrimary.withValues(alpha: 0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(8.gr),
          border:
              isSelected ? Border.all(color: context.colorTheme.textPrimary.withValues(alpha: 0.3), width: 1) : null,
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(4.gw),
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(15),
                borderRadius: BorderRadius.circular(20.gr),
              ),
              child: Image.asset(
                _getCurrencyFlag(currency),
                width: 28.gw,
                height: 28.gh,
              ),
            ),
            12.horizontalSpace,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getCurrencyName(currency),
                  style: isSelected ? context.textTheme.primary.fs14.w600 : context.textTheme.primary.fs14.w400,
                ),
                Text(
                  currency,
                  style: context.textTheme.regular.fs12.w400,
                ),
              ],
            ),
            const Spacer(),
            isSelected
                ? _buildEditableAmountField(context)
                : Container(
                    width: 150.gw,
                    height: 36.gh,
                    padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 6.gh),
                    decoration: BoxDecoration(
                      color: Colors.grey.withAlpha(15),
                      borderRadius: BorderRadius.circular(6.gr),
                    ),
                    alignment: Alignment.centerRight,
                    child: Text(
                      amount,
                      style: context.textTheme.regular.fs18.w600,
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditableAmountField(BuildContext context) {
    return Container(
      width: 150.gw,
      height: 36.gh,
      padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 0),
      child: TextField(
        controller: _amountController,
        focusNode: _amountFocusNode,
        readOnly: true, // Prevent direct input but allow focus
        showCursor: _showKeyboard,
        cursorColor: context.colorTheme.textPrimary,
        cursorWidth: 2,
        textAlign: TextAlign.right,
        style: context.textTheme.primary.fs18.w600.copyWith(
          color: context.colorTheme.textPrimary,
        ),
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 6.gh),
          isDense: true,
          hintText: '0',
          hintStyle: context.textTheme.primary.fs18.w600.copyWith(
            color: context.colorTheme.textPrimary.withValues(alpha: 0.5),
          ),
        ),
        keyboardType: TextInputType.none,
      ),
    );
  }

  String _getCurrencyFlag(String currency) {
    final currencyCode = currency.toLowerCase();
    switch (currencyCode) {
      case 'usd':
        return Assets.usFlagIcon;
      case 'cny':
        return Assets.cnFlagIcon;
      case 'hkd':
        return Assets.hkFlagIcon;
      default:
        return 'assets/icons/$currencyCode.png';
    }
  }

  String _getCurrencyName(String currency) {
    switch (currency) {
      case 'USD':
        return '美元';
      case 'CNY':
        return '人民币';
      case 'HKD':
        return '港币';
      default:
        return currency;
    }
  }
}
