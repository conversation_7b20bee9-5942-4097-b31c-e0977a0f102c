import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

import '../../../shared/widgets/otp_field.dart';
import '../../account/logic/otp/otp_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';

// register_form.dart
class RegisterForm extends StatelessWidget {
  final TextEditingController mobileController;
  final TextEditingController codeController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  final TextEditingController inviteCodeController;
  final OtpState otpState;
  final VoidCallback onSendVerificationCode;

  const RegisterForm({
    super.key,
    required this.mobileController,
    required this.codeController,
    required this.passwordController,
    required this.confirmPasswordController,
    required this.inviteCodeController,
    required this.otpState,
    required this.onSendVerificationCode,
  });

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 400),
          childAnimationBuilder: (widget) => SlideAnimation(
            verticalOffset: 30.0,
            child: FadeInAnimation(
              child: widget,
            ),
          ),
          children: [
            OtpField(
              mobileController: mobileController,
              codeController: codeController,
              otpState: otpState,
              onSendCode: onSendVerificationCode,
            ),
            15.verticalSpace,
            TextFieldWidget(
              controller: passwordController,
              hintText: 'registerPasswordHint'.tr(),
              textInputType: TextInputType.visiblePassword,
              prefixIcon: SvgPicture.asset(
                Assets.lockIcon,
                fit: BoxFit.scaleDown,
                width: 18.gw,
                height: 18.gh,
              ),
              obscureText: true,
              passwordIcon: true,
            ),
            15.verticalSpace,
            TextFieldWidget(
              controller: confirmPasswordController,
              hintText: 'registerConfirmPassword'.tr(),
              textInputType: TextInputType.visiblePassword,
              prefixIcon: SvgPicture.asset(
                Assets.lockIcon,
                fit: BoxFit.scaleDown,
                width: 18.gw,
                height: 18.gh,
              ),
              obscureText: true,
              passwordIcon: true,
            ),
            15.verticalSpace,
            TextFieldWidget(
              controller: inviteCodeController,
              hintText: 'registerInviteCode'.tr(),
              textInputType: TextInputType.text,
              prefixIcon: SvgPicture.asset(
                Assets.mailIcon,
                fit: BoxFit.scaleDown,
                width: 18.gw,
                height: 18.gh,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class RegisterFooter extends StatelessWidget {
  const RegisterFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.synchronized(
      duration: const Duration(milliseconds: 500),
      child: FadeInAnimation(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'alreadyHaveAnAccount'.tr(),
              style: context.textTheme.regular.w500,
            ),
            5.horizontalSpace,
            AnimationConfiguration.synchronized(
              duration: const Duration(milliseconds: 300),
              child: ScaleAnimation(
                scale: 0.95,
                child: GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Text(
                    'loginLoginNow'.tr(),
                    style: context.textTheme.primary.w500.copyWith(
                      color: context.theme.primaryColor,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
