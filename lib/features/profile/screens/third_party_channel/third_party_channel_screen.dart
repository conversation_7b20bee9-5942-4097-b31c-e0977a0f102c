import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart' show SvgPicture;
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_cubit.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_state.dart';
import 'package:gp_stock_app/features/account/widgets/withdraw_password_dialog.dart';
import 'package:gp_stock_app/features/profile/domain/models/third_party_channel/third_party_channel.dart';
import 'package:gp_stock_app/features/profile/logic/third_party_channel/third_party_channel_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';

import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

enum ThirdPartyTransactionType {
  deposit,
  withdraw;

  int get value => switch (this) {
        ThirdPartyTransactionType.deposit => 1,
        ThirdPartyTransactionType.withdraw => 2,
      };
}

class ThirdPartyChannelScreen extends StatefulWidget {
  const ThirdPartyChannelScreen(
      {super.key, this.showAppBar = true, this.transactionType = ThirdPartyTransactionType.deposit});

  final bool showAppBar;
  final ThirdPartyTransactionType transactionType;
  @override
  State<ThirdPartyChannelScreen> createState() => _ThirdPartyChannelScreenState();
}

class _ThirdPartyChannelScreenState extends State<ThirdPartyChannelScreen> {
  int selectedPaymentMethod = 0;
  int selectedPaymentType = 0;
  final amountController = TextEditingController();
  @override
  void initState() {
    super.initState();
    context.read<ThirdPartyChannelCubit>().getThirdPartyChannelList(widget.transactionType.value);
    context.read<ThirdPartyChannelCubit>().getUserWalletList();
    if (widget.transactionType == ThirdPartyTransactionType.withdraw) {
      context.read<ThirdPartyChannelCubit>().getUserWalletList();
    }
  }

  @override
  void dispose() {
    amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      appBar: widget.showAppBar
          ? AppBar(
              title: Text(
                'third'.tr(),
              ),
            )
          : null,
      body: BlocConsumer<ThirdPartyChannelCubit, ThirdPartyChannelState>(
        listener: (context, state) {
          if (state.channelListStatus == DataStatus.failed) {
            GPEasyLoading.showToast(state.error ?? 'somethingWentWrong'.tr());
          }
          if (state.updatingField == ThirdPartyChannelField.payment) {
            if (state.paymentStatus == DataStatus.loading) {
              GPEasyLoading.showLoading(message: 'loading'.tr());
            }
            if (state.paymentStatus == DataStatus.success && state.paymentResponse != null) {
              GPEasyLoading.showToast('success'.tr());
              launchUrl(Uri.parse(state.paymentResponse!.payUrl), mode: LaunchMode.externalApplication);
              setState(() {
                amountController.clear();
              });
            }
            if (state.paymentStatus == DataStatus.failed) {
              GPEasyLoading.showToast(state.error ?? 'errorMsg'.tr());
            }
          }
        },
        builder: (context, state) {
          if (state.channelListStatus == DataStatus.loading) {
            return _buildLoadingShimmer();
          } else if (state.channelListStatus == DataStatus.success) {
            return SingleChildScrollView(
              child: Column(
                children: [
                  _buildPaymentMethods(state.channels!),
                  _buildPaymentMethods2(state.channels!),
                  _buildWalletSelection(),
                  _buildDepositAmount(state.channels!, amountController),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: CustomMaterialButton(
                      onPressed: amountController.text.isNotEmpty
                          ? () {
                              return switch (widget.transactionType) {
                                ThirdPartyTransactionType.deposit => context.read<ThirdPartyChannelCubit>().submit(
                                      currentChannelIndex: selectedPaymentMethod,
                                      selectedPaymentTypeIndex: selectedPaymentType,
                                      amount: amountController.text,
                                    ),
                                ThirdPartyTransactionType.withdraw => showDialog(
                                    context: context,
                                    builder: (_) => BlocProvider.value(
                                      value: context.read<WithdrawalCubit>(),
                                      child: WithdrawPasswordDialog(
                                        withdrawAmount: amountController.text,
                                        userBankId: context.read<ThirdPartyChannelCubit>().state.selectedWallet!.id!,
                                        channelId: state.channels![selectedPaymentMethod]
                                            .payTypeList[selectedPaymentType].payTypeId,
                                        type: 3,
                                      ),
                                    ),
                                  ),
                              };
                            }
                          : null,
                      buttonText: 'submit'.tr(),
                      color: context.theme.primaryColor,
                      borderColor: context.theme.primaryColor,
                      borderRadius: 5.gr,
                      textColor: Colors.white,
                      fontSize: 13.gr,
                    ),
                  ),
                  const SizedBox(height: 16.0),
                  SupportWidget(),
                  const SizedBox(height: kBottomNavigationBarHeight),
                ],
              ),
            );
          } else if (state.channelListStatus == DataStatus.failed) {
            return _buildErrorView(state.error ?? 'somethingWentWrong'.tr());
          } else {
            return const SizedBox.shrink();
          }
        },
      ),
    );
  }

  Widget _buildLoadingShimmer() {
    return Padding(
      padding: EdgeInsets.all(16.gr),
      child: Column(
        children: [
          // Payment Methods Section
          ShadowBox(
            child: ShimmerWidget(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Container(
                    height: 24.gh,
                    width: 120.gw,
                    margin: EdgeInsets.all(16.gr),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                  Wrap(
                    spacing: 8.gr,
                    runSpacing: 8.gh,
                    children: List.generate(
                      3,
                      (index) => Container(
                        padding: EdgeInsets.all(16.gr),
                        width: 0.26.gsw,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.gr),
                          border: Border.all(
                            color: Colors.grey[200]!,
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Container(
                              width: 40.gh,
                              height: 40.gh,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                            SizedBox(height: 4.gh),
                            Container(
                              height: 16.gh,
                              width: 60.gw,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4.gr),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16.gh),
          // Payment Types Section
          ShadowBox(
            child: ShimmerWidget(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Container(
                    height: 24.gh,
                    width: 100.gw,
                    margin: EdgeInsets.all(16.gr),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                  Wrap(
                    spacing: 8.gr,
                    runSpacing: 8.gh,
                    children: List.generate(
                      4,
                      (index) => Container(
                        height: 32.gh,
                        width: 80.gw,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.gr),
                          border: Border.all(
                            color: Colors.grey[200]!,
                            width: 1,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16.gh),
          // Deposit Amount Section
          ShadowBox(
            child: ShimmerWidget(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Container(
                    height: 24.gh,
                    width: 120.gw,
                    margin: EdgeInsets.all(16.gr),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                  Wrap(
                    spacing: 4.gr,
                    runSpacing: 8.gh,
                    children: List.generate(
                      6,
                      (index) => Container(
                        height: 32.gh,
                        width: 60.gw,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.gr),
                          border: Border.all(
                            color: Colors.grey[200]!,
                            width: 1,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 16.gh),
                  Container(
                    height: 48.gh,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethods(List<ThirdPartyChannel> channels) {
    return Container(
      padding: EdgeInsets.all(16.gr),
      child: ShadowBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'paymentMethods'.tr(),
              style: context.textTheme.primary.fs16.w400,
            ),
            8.verticalSpace,
            if (channels.isNotEmpty)
              Wrap(
                spacing: 8.gr,
                runSpacing: 8.gh,
                children: channels.map((channel) {
                  final isSelected = selectedPaymentMethod == channels.indexOf(channel);
                  return Stack(
                    children: [
                      GestureDetector(
                        onTap: () => setState(() {
                          selectedPaymentMethod = channels.indexOf(channel);
                        }),
                        child: Container(
                          padding: EdgeInsets.all(16.gr),
                          constraints: BoxConstraints(
                            maxWidth: 0.26.gsw,
                            minWidth: 0.26.gsw,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.gr),
                            border: Border.all(
                              color: isSelected ? context.theme.primaryColor : context.theme.dividerColor,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            spacing: 4.gh,
                            children: [
                              CachedNetworkImage(
                                imageUrl: channel.icon,
                                width: 40.gh,
                                height: 40.gh,
                                errorWidget: (context, url, error) => const SizedBox.shrink(),
                              ),
                              Text(channel.payWayName),
                            ],
                          ),
                        ),
                      ),
                      if (channel.recommended)
                        Positioned(
                          top: 5,
                          right: 5,
                          child: SvgPicture.asset(
                            Assets.hot,
                            width: 22.gw,
                            height: 22.gh,
                            colorFilter: ColorFilter.mode(
                              isSelected ? context.theme.primaryColor : context.colorTheme.textRegular,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                    ],
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethods2(List<ThirdPartyChannel> channels) {
    return Container(
      padding: EdgeInsets.all(16.gr),
      child: ShadowBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'paymentTypes'.tr(),
              style: context.textTheme.primary.fs16.w400,
            ),
            if (channels.isNotEmpty)
              Wrap(
                spacing: 8.gr,
                runSpacing: 8.gh,
                children: channels[selectedPaymentMethod]
                    .payTypeList
                    .map(
                      (payType) => FilterChip(
                        label: Text(payType.controllerTips),
                        onSelected: (value) => setState(() {
                          selectedPaymentType = channels[selectedPaymentMethod].payTypeList.indexOf(payType);
                        }),
                        showCheckmark: false,
                        backgroundColor: Colors.transparent,
                        selectedShadowColor: Colors.transparent,
                        selectedColor: context.theme.scaffoldBackgroundColor,
                        labelStyle: context.textTheme.primary.fs13.w400.copyWith(
                          color: selectedPaymentType == channels[selectedPaymentMethod].payTypeList.indexOf(payType)
                              ? Colors.white
                              : context.colorTheme.textRegular,
                        ),
                        selected: selectedPaymentType == channels[selectedPaymentMethod].payTypeList.indexOf(payType),
                        side: selectedPaymentType == channels[selectedPaymentMethod].payTypeList.indexOf(payType)
                            ? BorderSide.none
                            : BorderSide(
                                color: context.theme.dividerColor,
                                width: 1,
                              ),
                      ),
                    )
                    .toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWalletSelection() {
    if (widget.transactionType == ThirdPartyTransactionType.deposit) return const SizedBox.shrink();
    return BlocListener<WithdrawalCubit, WithdrawalState>(
      listenWhen: (previous, current) => previous.withdrawStatus != current.withdrawStatus,
      listener: (context, state) {
        if (state.withdrawStatus.isSuccess) {
          Helper.showFlutterToast('withdrawnSuccessfully'.tr());
          amountController.clear();
        } else if (state.withdrawStatus.isFailed) {
          Helper.showFlutterToast(state.error ?? 'invalidWithdrawPassword'.tr());
        }
      },
      child: Padding(
        padding: EdgeInsets.all(16.gr),
        child: Column(
          children: [
            BlocBuilder<ThirdPartyChannelCubit, ThirdPartyChannelState>(
              builder: (context, state) {
                if (state.walletsFetchStatus == DataStatus.loading) {
                  return ShadowBox(
                    child: ShimmerWidget(
                      height: 50.gh,
                      width: double.infinity,
                    ),
                  );
                } else if (state.walletsFetchStatus == DataStatus.failed) {
                  return ShadowBox(
                    child: Padding(
                      padding: EdgeInsets.all(16.gr),
                      child: Text(
                        state.error ?? 'Failed to load wallets',
                        style: context.textTheme.regular.w400.copyWith(color: Colors.red),
                      ),
                    ),
                  );
                } else if (state.walletsFetchStatus == DataStatus.success) {
                  return ShadowBox(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      spacing: 12,
                      children: [
                        Text(
                          'selectWallet'.tr(),
                          style: context.textTheme.primary.fs16.w400,
                        ),
                        CommonDropdown(
                          hintText: 'selectWallet'.tr(),
                          onChanged: (value) {
                            final selectedWallet = state.wallets?.firstWhere((e) => e.id.toString() == value.id);
                            context.read<ThirdPartyChannelCubit>().updateSelectedWallet(selectedWallet);
                          },
                          selectedItem: state.selectedWallet == null
                              ? null
                              : DropDownValue(
                                  id: state.selectedWallet?.id.toString(),
                                  value:
                                      '${state.selectedWallet?.bankCode ?? ''} ${state.selectedWallet?.payAddress ?? ''}',
                                  icon: state.selectedWallet?.icon,
                                ),
                          dropDownValue: (state.wallets ?? [])
                              .map((e) => DropDownValue(
                                  id: e.id.toString(),
                                  value: '${e.bankCode ?? ''} ${e.payAddress ?? ''}',
                                  icon: e.icon,
                                  code: e))
                              .toList(),
                          showSearchBox: false,
                          itemBuilder: (context, item, isDisabled, isSelected) {
                            return Container(
                              margin: EdgeInsets.symmetric(horizontal: 10.0.gw),
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: 8.0.gh),
                                child: Row(
                                  children: [
                                    if (item.icon != null && item.icon!.isNotEmpty)
                                      CachedNetworkImage(
                                        imageUrl: item.icon ?? '',
                                        width: 40.gw,
                                        height: 40.gw,
                                        fit: BoxFit.cover,
                                        errorWidget: (context, url, error) => Icon(
                                          Icons.account_balance_wallet,
                                          size: 40.gw,
                                          color: context.colorTheme.textPrimary,
                                        ),
                                      )
                                    else
                                      Icon(
                                        Icons.account_balance_wallet,
                                        size: 40.gw,
                                        color: context.colorTheme.textPrimary,
                                      ),
                                    SizedBox(width: 10.gw),
                                    Expanded(
                                      child: Text(
                                        '${item.code.bankCode ?? ''} ${item.code.payAddress ?? ''}',
                                        style: context.textTheme.primary.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                        Row(
                          children: [
                            Spacer(flex: 2),
                            ElevatedButton.icon(
                              onPressed: () {
                                Navigator.pushNamed(context, routeAddWallet).then((result) {
                                  if (result == true && mounted) {
                                    // Refresh wallet list after successful addition
                                    getIt<ThirdPartyChannelCubit>().getUserWalletList();
                                  }
                                });
                              },
                              icon: const Icon(Icons.add, size: 18, color: Colors.white),
                              label: Text(
                                'addWallet'.tr(),
                                style: context.textTheme.primary.fs12.w400.copyWith(color: Colors.white),
                              ),
                            )
                          ],
                        )
                      ],
                    ),
                  );
                } else {
                  return ShadowBox(
                    child: Padding(
                      padding: EdgeInsets.all(16.gr),
                      child: Text(
                        'noWalletsAvailable'.tr(),
                        style: context.textTheme.regular.w400,
                      ),
                    ),
                  );
                }
              },
            ),
            SizedBox(height: 8.gh),
            // Add Wallet Button
            if (widget.transactionType == ThirdPartyTransactionType.deposit)
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(context, routeAddWallet).then((result) {
                        if (result == true && mounted) {
                          // Refresh wallet list after successful addition
                          context.read<ThirdPartyChannelCubit>().getUserWalletList();
                        }
                      });
                    },
                    icon: const Icon(Icons.add, size: 18, color: Colors.white),
                    label: Text(
                      'addWallet'.tr(),
                      style: context.textTheme.primary.fs12.w400.copyWith(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.theme.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.gr),
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDepositAmount(List<ThirdPartyChannel> channels, TextEditingController controller) {
    String min = '0';
    String max = '0';
    if (channels.isNotEmpty) {
      min = channels[selectedPaymentMethod].payTypeList[selectedPaymentType].amountMinLimit.toStringAsFixed(0);
      max = channels[selectedPaymentMethod].payTypeList[selectedPaymentType].amountMaxLimit.toStringAsFixed(0);
    }

    return Container(
      padding: EdgeInsets.all(16.gr),
      child: ShadowBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'depositAmount'.tr(),
              style: context.textTheme.primary.fs16.w400,
            ),
            if (channels.isNotEmpty)
              Wrap(
                spacing: 4.gr,
                children: channels[selectedPaymentMethod]
                    .payTypeList
                    .expand((payType) => payType.amountList.split(',').map(
                          (amount) => FilterChip(
                            label: Text(amount),
                            onSelected: (value) => setState(() {
                              controller.text = amount;
                            }),
                            showCheckmark: false,
                            backgroundColor: Colors.transparent,
                            selectedShadowColor: Colors.transparent,
                            selectedColor: context.theme.scaffoldBackgroundColor,
                            labelStyle: context.textTheme.primary.fs13.w400.copyWith(
                              color: context.colorTheme.textRegular,
                            ),
                            side: BorderSide(
                              color: context.theme.dividerColor,
                              width: 1,
                            ),
                          ),
                        ))
                    .toList(),
              ),
            TextField(
              controller: controller,
              decoration: InputDecoration(
                filled: true,
                hintText: '$min ~ $max',
                hintStyle: context.textTheme.primary.fs13.w400.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              onChanged: (value) => setState(() {}),
            ),
            16.verticalSpace,
            BlocSelector<AccountInfoCubit, AccountInfoState, ({AccountInfoData? accountInfoData})>(
              selector: (state) => (accountInfoData: state.accountInfo),
              builder: (context, accountState) {
                return Column(
                  children: [
                    Row(
                      spacing: 8,
                      children: [
                        Text(
                          'availableBalance'.tr(),
                          style: context.textTheme.regular.fs13.w400,
                        ),
                        FlipText(
                          accountState.accountInfoData?.usableCash ?? 0,
                        )
                      ],
                    ),
                  ],
                );
              },
            ),
            4.verticalSpace,
            Text(
              '* ${'minimumAmount'.tr()}: $min , ${'maximumAmount'.tr()}: $max',
              style: context.textTheme.regular.fs12.w400.copyWith(
                color: Colors.orange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48.gr,
            color: context.colorTheme.stockRed,
          ),
          16.verticalSpace,
          Text(
            errorMessage,
            style: context.textTheme.primary.fs16.w400.copyWith(
              color: context.colorTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          24.verticalSpace,
          ElevatedButton(
            onPressed: () {
              context.read<ThirdPartyChannelCubit>().getThirdPartyChannelList(widget.transactionType.value);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: context.theme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.gr),
              ),
              padding: EdgeInsets.symmetric(horizontal: 24.gw, vertical: 12.gh),
            ),
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }
}
