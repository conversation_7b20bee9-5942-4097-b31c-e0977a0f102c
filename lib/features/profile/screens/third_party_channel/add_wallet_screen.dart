import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/logic/third_party_channel/third_party_channel_cubit.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';

import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AddWalletScreen extends StatefulWidget {
  const AddWalletScreen({super.key});

  @override
  State<AddWalletScreen> createState() => _AddWalletScreenState();
}

class _AddWalletScreenState extends State<AddWalletScreen> {
  final TextEditingController _walletAddressController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String? _selectedBankCode;
  bool _isFormValid = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _walletAddressController.dispose();
    super.dispose();
  }

  void _validateForm() {
    setState(() {
      _isFormValid = _walletAddressController.text.isNotEmpty && _selectedBankCode != null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('addWallet'.tr()),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: BlocConsumer<ThirdPartyChannelCubit, ThirdPartyChannelState>(
        listenWhen: (previous, current) => previous.bindWalletStatus != current.bindWalletStatus,
        listener: (context, state) {
          if (state.updatingField == ThirdPartyChannelField.bindWallet) {
            if (state.bindWalletStatus == DataStatus.loading) {
              GPEasyLoading.showLoading(message: 'loading'.tr());
            } else if (state.bindWalletStatus == DataStatus.success) {
              GPEasyLoading.dismiss();
              GPEasyLoading.showToast('walletAddedSuccessfully'.tr());
              Navigator.of(context).pop(true); // Return true to indicate success
            } else if (state.bindWalletStatus == DataStatus.failed) {
              GPEasyLoading.dismiss();
              GPEasyLoading.showToast(state.error ?? 'failedToAddWallet'.tr());
            }
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.all(16.gr),
              child: ShadowBox(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Wallet Address Field
                      Text(
                        'walletAddress'.tr(),
                        style: context.textTheme.primary.fs16,
                      ),
                      SizedBox(height: 8.gh),
                      TextFieldWidget(
                        controller: _walletAddressController,
                        hintText: 'enterWalletAddress'.tr(),
                        onChanged: (value) {
                          _validateForm();
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'walletAddressRequired'.tr();
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 24.gh),

                      // Wallet Channel Dropdown
                      Text(
                        'walletChannel'.tr(),
                        style: context.textTheme.primary.fs16,
                      ),
                      SizedBox(height: 8.gh),
                      _buildChannelDropdown(state),
                      SizedBox(height: 40.gh),

                      // Submit Button
                      CommonButton(
                        onPressed: _isFormValid
                            ? () {
                                if (_formKey.currentState!.validate()) {
                                  context.read<ThirdPartyChannelCubit>().bindUserWallet(
                                        bankCode: _selectedBankCode!,
                                        payAddress: _walletAddressController.text.trim(),
                                      );
                                }
                              }
                            : null,
                        title: 'submit'.tr(),
                        backgroundColor: context.theme.primaryColor,
                        radius: 5.gr,
                        textColor: Colors.white,
                        fontSize: 13.gr,
                      ),
                      SizedBox(height: 24.gh),

                      SupportWidget(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildChannelDropdown(ThirdPartyChannelState state) {
    if (state.channelListStatus == DataStatus.loading) {
      return const Center(child: CircularProgressIndicator());
    } else if (state.channelListStatus == DataStatus.failed) {
      return Text(
        state.error ?? 'failedToLoadChannels'.tr(),
        style: context.textTheme.regular.w400.copyWith(color: Colors.red),
      );
    } else if (state.channels != null && state.channels!.isNotEmpty) {
      // Create dropdown items from channels
      final dropdownItems = state.channels!.map((channel) {
        return DropDownValue(
          id: channel.payWayCode,
          value: channel.payWayName,
          icon: channel.icon,
        );
      }).toList();

      return CommonDropdown(
        hintText: 'selectWalletChannel'.tr(),
        dropDownValue: dropdownItems,
        selectedItem: _selectedBankCode != null
            ? dropdownItems.firstWhere(
                (item) => item.id == _selectedBankCode,
                orElse: () => dropdownItems.first,
              )
            : null,
        onChanged: (value) {
          setState(() {
            _selectedBankCode = value.id;
            _validateForm();
          });
        },
        showSearchBox: false,
        itemBuilder: (context, item, isDisabled, isSelected) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 10.0.gw),
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 8.0.gh),
              child: Row(
                children: [
                  if (item.icon != null && item.icon!.isNotEmpty)
                    Image.network(
                      item.icon!,
                      width: 40.gw,
                      height: 40.gw,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.account_balance_wallet,
                        size: 40.gw,
                        color: context.colorTheme.textPrimary,
                      ),
                    )
                  else
                    Icon(
                      Icons.account_balance_wallet,
                      size: 40.gw,
                      color: context.colorTheme.textPrimary,
                    ),
                  SizedBox(width: 10.gw),
                  Expanded(
                    child: Text(
                      item.value ?? '',
                      style: context.textTheme.primary.w400,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } else {
      return Text(
        'noChannelsAvailable'.tr(),
        style: context.textTheme.regular.w400,
      );
    }
  }
}
