import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../../shared/constants/enums.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';

import '../../../domain/models/vip_next_level/next_user_level_model.dart';
import '../../../logic/vip/vip_cubit.dart';
import '../../../logic/vip/vip_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class VipUpgradeCard extends StatelessWidget {
  const VipUpgradeCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VipCubit, VipState>(
      buildWhen: (previous, current) =>
          previous.nextLevelStatus != current.nextLevelStatus || previous.nextUserLevel != current.nextUserLevel,
      builder: (context, state) {
        if (state.nextLevelStatus == DataStatus.loading) {
          Widget buildShimmerProgressRow() {
            return ShimmerWidget(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 80.gw,
                            height: 16.gh,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(4.gr),
                            ),
                          ),
                          SizedBox(width: 8.gw),
                          Container(
                            width: 60.gw,
                            height: 16.gh,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(4.gr),
                            ),
                          ),
                        ],
                      ),
                      Container(
                        width: 100.gw,
                        height: 16.gh,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4.gr),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.gh),
                  Container(
                    width: double.infinity,
                    height: 4.gh,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2.gr),
                    ),
                  ),
                ],
              ),
            );
          }

          return Container(
            padding: EdgeInsets.all(16.gw),
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(12.gr),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ShimmerWidget(
                  child: Container(
                    width: 120.gw,
                    height: 24.gh,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ),
                SizedBox(height: 12.gh),
                buildShimmerProgressRow(),
                SizedBox(height: 8.gh),
                Divider(color: context.theme.dividerColor),
                SizedBox(height: 8.gh),
                buildShimmerProgressRow(),
                SizedBox(height: 12.gh),
              ],
            ),
          );
        }

        if (state.nextLevelStatus == DataStatus.failed || state.nextUserLevel == null) {
          return const Center(child: Icon(Icons.error));
        }

        return Container(
          padding: EdgeInsets.all(16.gw),
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(12.gr),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTitle(context),
              const SizedBox(height: 12),
              _buildInviteFriendsRow(context, state.nextUserLevel!),
              const SizedBox(height: 8),
              _buildDivider(context),
              const SizedBox(height: 8),
              _buildTotalRechargeRow(context, state.nextUserLevel!),
              const SizedBox(height: 12),
              // _buildDivider(context),
              // const SizedBox(height: 8),
              // _buildInviteButtonRow(context),
            ],
          ),
        );
      },
    );
  }

  /// Builds the title of the card.
  Widget _buildTitle(BuildContext context) {
    return Text(
      'upgradeVipTitle'.tr(),
      style: context.textTheme.primary.fs16.w600,
    );
  }

  /// Builds the "Invite Friends" row with a progress bar.
  Widget _buildInviteFriendsRow(BuildContext context, NextUserLevelModel nextUserLevel) {
    final int currentInvites = nextUserLevel.userInviteNumber;
    final int inviteTarget = nextUserLevel.nextLevelInviteNumber;
    final double progress = inviteTarget > 0 ? currentInvites / inviteTarget : 0.0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        'inviteFriends'.tr(),
                        style: context.textTheme.primary.w400,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '$currentInvites/$inviteTarget',
                        style: context.textTheme.primary.fs12.w400.copyWith(
                          color: context.theme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    '$inviteTarget ${'person'.tr()}',
                    style: context.textTheme.primary.w600.copyWith(
                      color: context.theme.primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: progress.clamp(0.0, 1.0),
                backgroundColor: context.theme.primaryColor.withValues(alpha: 0.1),
                valueColor: AlwaysStoppedAnimation<Color>(context.theme.primaryColor),
                minHeight: 4,
                borderRadius: BorderRadius.circular(2),
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  /// Builds the "Total Recharge" row with a progress bar.
  Widget _buildTotalRechargeRow(BuildContext context, NextUserLevelModel nextUserLevel) {
    final double currentRecharge = nextUserLevel.userRechargeAmount;
    final double rechargeTarget = nextUserLevel.nextLevelRechargeAmount;
    final double progress = rechargeTarget > 0 ? currentRecharge / rechargeTarget : 0.0;
    final double remainRechargeAmount = nextUserLevel.remainRechargeAmount;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        'totalRecharge'.tr(),
                        style: context.textTheme.primary.w400,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${currentRecharge.toStringAsFixed(0)}/${rechargeTarget.toStringAsFixed(0)}',
                        style: context.textTheme.primary.fs12.w400.copyWith(
                          color: context.theme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    '${remainRechargeAmount.toStringAsFixed(2)} CNY',
                    style: context.textTheme.primary.w600.copyWith(
                      color: context.theme.primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: progress.clamp(0.0, 1.0),
                backgroundColor: context.theme.primaryColor.withValues(alpha: 0.1),
                valueColor: AlwaysStoppedAnimation<Color>(context.theme.primaryColor),
                minHeight: 4,
                borderRadius: BorderRadius.circular(2),
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  /// Builds a horizontal divider.
  Widget _buildDivider(BuildContext context) {
    return Divider(
      color: context.theme.dividerColor,
      height: 1,
    );
  }
}
