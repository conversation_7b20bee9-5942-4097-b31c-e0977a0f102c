import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../../shared/constants/assets.dart';
import '../../../../../shared/constants/enums.dart';

import '../../../../../shared/widgets/buttons/common_button.dart';
import '../../../../../shared/widgets/shimmer/shimmer_widget.dart';
import '../../../domain/models/mission_activity/sign_in_model.dart';
import '../../../logic/mission_center/cubit/mission_activity_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class SignInStreakCard extends StatelessWidget {
  const SignInStreakCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MissionActivityCubit, MissionActivityState>(
      builder: (context, state) {
        if (state.signInLogStatus == DataStatus.loading) {
          return const SignInStreakShimmer();
        }

        if (state.signInLogStatus == DataStatus.failed) {
          return Center(child: Text(state.error ?? 'signInLogError'.tr()));
        }

        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.gw),
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(12.gr),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'signInActivity'.tr(),
                style: context.textTheme.primary.fs16.w600,
              ),
              16.verticalSpace,
              _buildCalendar(context, state),
              16.verticalSpace,
              _buildSignInButton(context, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCalendar(BuildContext context, MissionActivityState state) {
    final weekDays = ['mon'.tr(), 'tue'.tr(), 'wed'.tr(), 'thu'.tr(), 'fri'.tr(), 'sat'.tr(), 'sun'.tr()];
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final firstWeekdayOfMonth = firstDayOfMonth.weekday - 1;
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;

    final calendarDays = <Widget>[];

    // Add empty cells for days before 1st
    for (int i = 0; i < firstWeekdayOfMonth; i++) {
      calendarDays.add(const SizedBox());
    }

    // Add day cells up to current day only
    for (int day = 1; day <= today.day; day++) {
      final date = DateTime(now.year, now.month, day);
      final dateStr = DateFormat('yyyy-MM-dd').format(date);
      final isToday = date.isAtSameMomentAs(today);
      final isPast = date.isBefore(today);

      final signInDay = state.signInDays.firstWhere(
        (d) => d.date == dateStr,
        orElse: () => const SignInDayModel(amount: 0, hasSign: false, date: ''),
      );

      calendarDays.add(
        _CalendarDay(
          date: day,
          amount: signInDay.amount ?? 0,
          hasSign: signInDay.hasSign ?? false,
          isToday: isToday,
          isPast: isPast,
        ),
      );
    }

    // Add empty cells for future days
    for (int day = today.day + 1; day <= daysInMonth; day++) {
      calendarDays.add(
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.gr),
            border: Border.all(
              color: context.theme.dividerColor,
            ),
          ),
          child: Center(
            child: Text(
              '$day',
              style: context.textTheme.regular.fs12.w400.copyWith(
                color: context.colorTheme.textRegular.withValues(alpha: 0.5),
              ),
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        Text(
          DateFormat('yyyy-MM').format(now),
          style: context.textTheme.primary.w600,
        ),
        12.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: weekDays
              .map((day) => Text(
                    day,
                    style: context.textTheme.regular.fs12.w400,
                  ))
              .toList(),
        ),
        12.verticalSpace,
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 7,
            mainAxisSpacing: 6.gh,
            crossAxisSpacing: 4.gw,
            childAspectRatio: 0.7,
          ),
          itemCount: calendarDays.length,
          itemBuilder: (context, index) => calendarDays[index],
        ),
      ],
    );
  }

  Widget _buildSignInButton(BuildContext context, MissionActivityState state) {
    return SizedBox(
      width: double.infinity,
      height: 35.gh,
      child: CommonButton(
        title: state.hasSign ? 'alreadySignedIn'.tr() : 'signIn'.tr(),
        showLoading: state.signInStatus == DataStatus.loading,
        enable: !state.hasSign,
        onPressed: state.hasSign ? null : () => context.read<MissionActivityCubit>().signIn(),
      ),
    );
  }
}

class _CalendarDay extends StatelessWidget {
  final int date;
  final double amount;
  final bool hasSign;
  final bool isToday;
  final bool isPast;

  const _CalendarDay({
    required this.date,
    required this.amount,
    required this.hasSign,
    required this.isToday,
    required this.isPast,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: isToday
            ? context.theme.primaryColor.withNewOpacity(0.1)
            : hasSign
                ? context.theme.primaryColor.withNewOpacity(0.01)
                : isPast
                    ? Colors.red.withNewOpacity(0.05)
                    : context.theme.scaffoldBackgroundColor.withNewOpacity(0.08),
        borderRadius: BorderRadius.circular(8.gr),
        border: Border.all(
          color: isToday
              ? context.theme.primaryColor
              : hasSign
                  ? context.theme.primaryColor.withNewOpacity(0.1)
                  : context.theme.dividerColor,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '$date',
            style: context.textTheme.primary.fs12.w400.copyWith(
              color: isToday ? context.theme.primaryColor : context.colorTheme.textPrimary,
            ),
          ),
          if (hasSign) ...[
            SvgPicture.asset(
              Assets.giftBoxOpenIcon,
              width: 20.gw,
              height: 20.gh,
            ),
            Text(
              '¥$amount',
              style: context.textTheme.primary.fs12.w600.copyWith(
                color: context.theme.primaryColor,
              ),
            ),
          ] else
            SvgPicture.asset(
              isPast ? Assets.giftBoxExpiredIcon : Assets.giftBoxIcon,
              width: 20.gw,
              height: 20.gh,
            ),
        ],
      ),
    );
  }
}

class SignInStreakShimmer extends StatelessWidget {
  const SignInStreakShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gr),
      ),
      child: ShimmerWidget(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Container(
              width: 120.gw,
              height: 20.gh,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.gr),
              ),
            ),
            16.verticalSpace,
            // Month
            Center(
              child: Container(
                width: 80.gw,
                height: 16.gh,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.gr),
                ),
              ),
            ),
            12.verticalSpace,
            // Week days
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: List.generate(
                7,
                (_) => Container(
                  width: 20.gw,
                  height: 14.gh,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                ),
              ),
            ),
            12.verticalSpace,
            // Calendar grid
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                mainAxisSpacing: 8.gh,
                crossAxisSpacing: 8.gw,
                childAspectRatio: 1,
              ),
              itemCount: 31,
              itemBuilder: (_, __) => Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.gr),
                ),
              ),
            ),
            16.verticalSpace,
            // Sign in button
            Container(
              width: double.infinity,
              height: 44.gh,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.gr),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
