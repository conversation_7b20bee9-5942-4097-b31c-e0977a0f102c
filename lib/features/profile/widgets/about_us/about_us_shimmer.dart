import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AboutUsShimmer extends StatelessWidget {
  const AboutUsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(12.gr),
          ),
          child: Column(
            children: List.generate(
              4,
              (index) => Container(
                padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gh),
                decoration: BoxDecoration(
                  border: index == 3
                      ? null
                      : Border(
                          bottom: BorderSide(
                            color: Colors.grey.withValues(alpha: 0.1),
                          ),
                        ),
                ),
                child: ShimmerWidget(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: 120.gw,
                        height: 24.gh,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4.gr),
                        ),
                      ),
                      Icon(
                        Icons.chevron_right,
                        size: 24.gr,
                        color: Colors.grey[300],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
