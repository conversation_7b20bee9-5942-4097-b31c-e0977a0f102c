import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/sign_in/logic/sign_in/sign_in_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';

import '../../../../shared/constants/assets.dart';
import '../../../../shared/routes/routes.dart';
import 'profile_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ProfileActionButton extends StatelessWidget {
  const ProfileActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 347.gw,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.gr),
              color: context.theme.cardColor,
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gh),
            child: AnimationLimiter(
              child: Column(
                spacing: 20.gh,
                children: AnimationConfiguration.toStaggeredList(
                  duration: const Duration(milliseconds: 600),
                  childAnimationBuilder: (widget) => SlideAnimation(
                    verticalOffset: 30.0,
                    child: FadeInAnimation(
                      child: widget,
                    ),
                  ),
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.vipIcon,
                            title: 'vip'.tr(),
                            onTap: () => context
                                .verifyAuth(() => Navigator.pushNamed(context, routeMissionCenter, arguments: true)),
                          ),
                        ),
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.missionIcon,
                            title: 'missionCenter'.tr(),
                            onTap: () => context.verifyAuth(() => Navigator.pushNamed(context, routeMissionCenter)),
                          ),
                        ),
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.withdrawalIcon,
                            title: 'depositWithdrawal'.tr(),
                            onTap: () => context.verifyAuth(() => _showActionDialog(context)),
                          ),
                        ),
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.thirdPartyIcon,
                            title: 'third'.tr(),
                            onTap: () => context.verifyAuth(
                              () => Navigator.pushNamed(context, routeDepositMain, arguments: {
                                "type": DepositType.third,
                              }),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.invitationIcon,
                            title: 'invitationRebate'.tr(),
                            onTap: () => context.verifyAuth(() => Navigator.pushNamed(context, routeInvite)),
                          ),
                        ),
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.questionIcon,
                            title: 'newbieQuestions'.tr(),
                            onTap: () => context.verifyAuth(() => Navigator.pushNamed(context, routeQuestions)),
                          ),
                        ),
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.authIcon,
                            title: 'realNameAuthentication'.tr(),
                            onTap: () => context.verifyAuth(() => Navigator.pushNamed(context, routeAuthN)),
                          ),
                        ),
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.settingsIcon,
                            title: 'systemSettings'.tr(),
                            onTap: () => context.verifyAuth(() => Navigator.pushNamed(context, routeSettings)),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.infoIcon,
                            title: 'aboutUs'.tr(),
                            onTap: () => Navigator.pushNamed(context, routeAboutUs),
                          ),
                        ),
                        Expanded(
                          child: BuildActionButton(
                            icon: Assets.exchangeIcon,
                            title: 'exchange'.tr(),
                            onTap: () => context.verifyAuth(() => Navigator.pushNamed(context, routeConvertRate)),
                          ),
                        ),
                        Expanded(
                          child: BlocBuilder<SignInCubit, SignInState>(
                            builder: (context, state) {
                              return Visibility(
                                visible: state.isSignedIn,
                                child: BuildActionButton(
                                  icon: Assets.logoutIcon,
                                  title: 'logout'.tr(),
                                  onTap: () => Helper.logoutUser(context, isNavToMain: true),
                                ),
                              );
                            },
                          ),
                        ),
                        Expanded(
                          child: const SizedBox(),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

Future<void> _showActionDialog(BuildContext context) async {
  final result = await showDialog<String>(
    context: context,
    builder: (context) => ScaleTransition(
      scale: Tween<double>(begin: 0.8, end: 1.0).animate(
        CurvedAnimation(
          parent: ModalRoute.of(context)!.animation!,
          curve: Curves.easeInOut,
        ),
      ),
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.gr),
        ),
        child: Container(
          padding: EdgeInsets.all(16.gr),
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(16.gr),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'depositWithdrawal'.tr(),
                    style: context.textTheme.primary.fs18.w600,
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: EdgeInsets.all(4.gr),
                      child: Icon(
                        Icons.close,
                        size: 24.gr,
                        color: context.theme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              24.verticalSpace,
              IntrinsicHeight(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ActionOptionButton(
                      icon: Assets.myAssetIcon,
                      title: 'topUpDeposit'.tr(),
                      onTap: () => Navigator.pop(context, 'deposit'),
                    ),
                    ActionOptionButton(
                      icon: Assets.withdrawalIcon,
                      title: 'cashOut'.tr(),
                      onTap: () => Navigator.pop(context, 'withdraw'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );

  if (result == 'deposit' && context.mounted) {
    context.verifyAuth(
      () => Navigator.pushNamed(context, routeDepositMain),
    );
  } else if (result == 'withdraw' && context.mounted) {
    context.verifyAuth(
      () => context.verifyRealName(
        () => Navigator.pushNamed(context, routeWithdrawMain),
      ),
    );
  }
}
