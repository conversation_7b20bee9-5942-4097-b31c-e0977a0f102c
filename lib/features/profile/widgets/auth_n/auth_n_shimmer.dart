import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../shared/mixin/animation.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class AuthNShimmer extends StatelessWidget with StaggeredAnimation {
  const AuthNShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 50.gh, left: 12.gw, right: 12.gw),
      child: Column(
        children: staggeredAnimationScale(
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(8.gr),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withNewOpacity(0.1),
                    blurRadius: 10,
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.gr),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Document Type Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'documentType'.tr(),
                          style: context.textTheme.primary.w500,
                        ),
                        ShimmerWidget(
                          height: 24.gh,
                          width: 80.gw,
                          radius: 10.gr,
                        ),
                      ],
                    ),
                    12.verticalSpace,
                    // Document Type Dropdown
                    ShimmerWidget(
                      height: 48.gh,
                      radius: 10.gr,
                    ),
                    20.verticalSpace,
                    // Name Field
                    Text(
                      'nameOnId'.tr(),
                      style: context.textTheme.primary.w500,
                    ),
                    12.verticalSpace,
                    ShimmerWidget(
                      height: 48.gh,
                      radius: 10.gr,
                    ),
                    20.verticalSpace,
                    // ID Number Field
                    Text(
                      'idNumber'.tr(),
                      style: context.textTheme.primary.w500,
                    ),
                    12.verticalSpace,
                    ShimmerWidget(
                      height: 48.gh,
                      radius: 10.gr,
                    ),
                    20.verticalSpace,
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
