import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/theme/custom_text_theme.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class SettingsAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool? centreTitle;

  const SettingsAppBar({super.key, required this.title, this.centreTitle});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: context.theme.cardColor,
      surfaceTintColor: Colors.transparent,
      elevation: 0,
      centerTitle: centreTitle,
      iconTheme: IconThemeData(
        color: context.colorTheme.textPrimary,
      ),
      title: Text(title, style: context.textTheme.primary.fs16.w500),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
