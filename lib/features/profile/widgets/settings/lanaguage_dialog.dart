import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../shared/app/extension/helper.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import '../../../../shared/widgets/alert_dilaog/dialog_helper.dart';
import '../../../../shared/widgets/buttons/custom_radio_button.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class LanguageDialog extends StatelessWidget {
  const LanguageDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final currentLocale = context.locale;

    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        height: 250,
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(8.gr),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.gw),
              child: Padding(
                padding: EdgeInsets.all(5.0.gr),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: context.supportedLocales.map((locale) {
                    return Column(
                      children: [
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          onTap: () async {
                            HapticFeedback.lightImpact();
                            try {
                              if (context.supportedLocales.contains(locale)) {
                                await context.setLocale(locale);
                                if (context.mounted) {
                                  Navigator.pop(context);
                                }
                              }
                            } catch (e) {
                              debugPrint('Error setting locale: $e');
                            }
                          },
                          title: Row(
                            children: [
                              SvgPicture.asset(
                                Helper().getLanguageIcon(locale),
                                width: 20.gw,
                                height: 20.gh,
                              ),
                              6.horizontalSpace,
                              Text(
                                Helper().getLanguageName(locale),
                                style: context.textTheme.primary.w500,
                              ),
                              if (locale == currentLocale) ...[
                                Spacer(),
                                CustomRadioButton(
                                  isSelected: true,
                                  onChange: (value) {},
                                ),
                              ]
                            ],
                          ),
                          selected: currentLocale == locale,
                          selectedColor: context.theme.primaryColor,
                          textColor: context.colorTheme.textRegular,
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
            Container(
              height: 8.gh,
              color: context.theme.scaffoldBackgroundColor,
            ),
            DialogFooter(),
            10.verticalSpace,
          ],
        ),
      ),
    );
  }
}
