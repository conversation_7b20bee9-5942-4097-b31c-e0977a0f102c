import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import '../widgets/wishlist_data_table.dart';

class WatchListScreen extends StatefulWidget {
  const WatchListScreen({super.key});

  @override
  State<WatchListScreen> createState() => _WatchListScreenState();
}

class _WatchListScreenState extends State<WatchListScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      context.read<WatchListCubit>().getWatchList(loadMore: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 4,
      child: Scaffold(
        appBar: TabBar(
          dividerHeight: 0,
          padding: EdgeInsets.zero,
          labelPadding: EdgeInsets.zero,
          indicatorPadding: EdgeInsets.zero,
          labelStyle: TextStyle(
            fontSize: 13.gsp,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).primaryColor,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 13.gsp,
            fontWeight: FontWeight.w600,
            color: Colors.grey[600],
          ),
          indicatorSize: TabBarIndicatorSize.label,
          indicator: UnderlineTabIndicator(
            borderSide: BorderSide(
              width: 2.0.gw,
              color: Theme.of(context).primaryColor,
              strokeAlign: BorderSide.strokeAlignCenter,
              style: BorderStyle.solid,
            ),
            insets: EdgeInsets.symmetric(horizontal: 16.0),
          ),
          tabs: [
            Tab(text: 'all'.tr()),
            Tab(text: MarketSymbol.CN.displayName.tr()),
            Tab(text: MarketSymbol.HK.displayName.tr()),
            Tab(text: MarketSymbol.US.displayName.tr()),
          ],
        ),
        body: Padding(
          padding: EdgeInsets.only(top: 10.0.gh),
          child: TabBarView(
            children: [
              WishListDataTable(),
              WishListDataTable(filter: MarketSymbol.CN),
              WishListDataTable(filter: MarketSymbol.HK),
              WishListDataTable(filter: MarketSymbol.US),
            ],
          ),
        ),
      ),
    );
  }
}
