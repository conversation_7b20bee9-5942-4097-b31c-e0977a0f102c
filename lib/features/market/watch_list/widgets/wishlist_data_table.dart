import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../shared/constants/enums.dart';
import '../../../../shared/models/stock/stock_response.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';

import '../../../../shared/widgets/market_table/market_table_row.dart';
import '../../../../shared/widgets/sort_header.dart';
import '../../../account/widgets/table_empty.dart';
import '../logic/watch_list_cubit.dart';
import '../logic/watch_list_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class WishListDataTable extends StatefulWidget {
  final int? limit;
  final MarketSymbol? filter;

  const WishListDataTable({super.key, this.limit, this.filter});

  @override
  State<WishListDataTable> createState() => _WishListDataTableState();
}

class _WishListDataTableState extends State<WishListDataTable> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    if (widget.filter != null) {
      context.read<WatchListCubit>().getWatchList(market: widget.filter?.name);
      _scrollController.addListener(_onScroll);
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      context.read<WatchListCubit>().getWatchList(loadMore: true, market: widget.filter?.name);
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildTableContainer();
  }

  Widget _buildTableContainer() {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [
          BoxShadow(
            color: context.theme.shadowColor,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(18.gr),
        child: Column(
          children: [
            _buildTableHeader(),
            10.verticalSpace,
            Divider(color: context.theme.dividerColor),
            _buildTableContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildTableHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 3,
          child: Text(
            'name'.tr(),
            style: _headerTextStyle,
          ),
        ),
        BlocBuilder<WatchListCubit, WatchListState>(
          builder: (context, state) => Expanded(
            flex: 3,
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: SortHeader(
                    title: 'currentPrice'.tr(),
                    sortType: state.sortByPriceAsc,
                    onTap: context.read<WatchListCubit>().handleSortByPrice,
                    textStyle: FontPalette.medium12,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: SortHeader(
                    title: 'rise_fall'.tr(),
                    sortType: state.sortByChangeAsc,
                    onTap: context.read<WatchListCubit>().handleSortByChange,
                    textStyle: FontPalette.medium12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTableContent() {
    return BlocBuilder<WatchListCubit, WatchListState>(
      builder: (context, state) {
        if (state.getWatchListStatus == DataStatus.loading &&
            (state.watchList == null || widget.filter != null) &&
            !state.isLoadMore) {
          return _buildLoadingList();
        }

        if (state.getWatchListStatus == DataStatus.failed && state.watchList == null) {
          return Center(
            child: TableEmptyWidget(),
          );
        }

        final watchList = state.watchList;
        if (watchList == null || watchList.isEmpty) {
          return Center(
            child: Text('watchlist_empty'.tr()),
          );
        }

        final filteredList = watchList;

        return RefreshIndicator(
          onRefresh: () async {
            await context.read<WatchListCubit>().getWatchList(market: widget.filter?.name);
          },
          child: ListView.builder(
            controller: _scrollController,
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: watchList.length,
            itemBuilder: (context, index) {
              final item = watchList[index];
              if (index >= filteredList.length) {
                return state.hasMore
                    ? const Center(
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CircularProgressIndicator(),
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Center(
                          child: Text(
                            'noMoreStocksToLoad'.tr(),
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      );
              }
              return MarketTableRow(
                data: StockItem(
                  name: item.name,
                  symbol: item.symbol,
                  latestPrice: item.latestPrice,
                  market: item.market,
                  gain: item.gain,
                  securityType: item.securityType,
                ),
                tabType: TradeTabType.Quotes,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: EdgeInsets.only(bottom: 8.gh),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.gr),
            child: ShimmerWidget(
              height: 45.gh,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }

  TextStyle get _headerTextStyle => FontPalette.medium14.copyWith(
        color: context.colorTheme.textRegular,
      );
}
