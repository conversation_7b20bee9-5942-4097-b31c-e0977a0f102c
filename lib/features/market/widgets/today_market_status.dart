import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class TodayMarketStatus extends StatelessWidget {
  final int? upCount;
  final int? flatCount;
  final int? downCount;

  const TodayMarketStatus({
    super.key,
    this.upCount,
    this.flatCount,
    this.downCount,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildStatus(
          context: context,
          icon: LucideIcons.arrow_up,
          label: "Up",
          count: upCount,
          color: Colors.green.shade600,
        ),
        const SizedBox(width: 16),
        _buildStatus(
          context: context,
          icon: LucideIcons.minus,
          label: "Flat",
          count: flatCount,
          color: Colors.grey,
        ),
        const SizedBox(width: 16),
        _buildStatus(
          context: context,
          icon: LucideIcons.arrow_down,
          label: "Down",
          count: downCount,
          color: Colors.red.shade600,
        ),
      ],
    );
  }

  Widget _buildStatus({
    required BuildContext context,
    required IconData icon,
    required String label,
    required int? count,
    required Color color,
  }) {
    if (count == null) return Container();
    return Row(
      children: [
        Icon(icon, color: color, size: 12),
        const SizedBox(width: 4),
        Text(
          "$label: ",
          style: context.textTheme.primary.copyWith(
            fontWeight: FontWeight.w600,
            color: color,
            fontSize: 11,
          ),
        ),
        Text(
          "$count",
          style: context.textTheme.primary.copyWith(
            fontWeight: FontWeight.w500,
            color: color,
            fontSize: 11,
          ),
        ),
      ],
    );
  }
}
