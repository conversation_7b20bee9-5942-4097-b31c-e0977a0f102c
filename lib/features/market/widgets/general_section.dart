import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/models/stock/stock_response.dart';
import '../../../shared/routes/routes.dart';

import '../../../shared/widgets/card/section_container.dart';
import '../../../shared/widgets/error/shared_error.dart';
import '../domain/models/plate_response.dart';
import '../logic/market/market_cubit.dart';
import 'general_card.dart';

class MarketGeneralSection extends StatelessWidget {
  const MarketGeneralSection({
    super.key,
    required this.type,
    required this.tab,
  });

  final int type;
  final TodaysTab tab;
  @override
  Widget build(BuildContext context) {
    return BlocSelector<MarketCubit, MarketState, (DataStatus, PlateResponse?)>(
      selector: (state) =>
          type == 1 ? (state.plateFetchStatusA, state.plateResponseA) : (state.plateFetchStatusB, state.plateResponseB),
      builder: (context, state) {
        final list = state.$2?.data?.records;
        if (state.$1 == DataStatus.loading && list == null) {
          return _buildLoading(context);
        }
        if (state.$1 == DataStatus.failed || list == null) {
          return const SharedError();
        }

        return _buildGrid(context, list);
      },
    );
  }

  Widget _buildLoading(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 0.98,
        ),
        itemCount: 8,
        itemBuilder: (context, index) {
          return ShimmerWidget(
            color: Theme.of(context).brightness == Brightness.dark ? Colors.grey[800] : Colors.white,
            child: SizedBox(
              child: Container(
                height: 130,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Color(0xFFF3F6FE),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildGrid(BuildContext context, List<StockItem> data) {
    return SectionContainer(
      title: 'industry_sectors'.tr(),
      showDivider: false,
      suffixIcon: data.length >= 6
          ? GestureDetector(
              onTap: () => Navigator.pushNamed(context, routePlateList),
              child: const Icon(Icons.arrow_forward_ios_rounded, size: 16),
            )
          : null,
      child: Padding(
        padding: EdgeInsets.only(top: 8.gh),
        child: Container(
          alignment: Alignment.center,
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 0.98,
            ),
            itemCount: data.length >= 6 ? 6 : data.length,
            itemBuilder: (context, index) {
              return MarketGeneralCard(data: data[index], tab: tab);
            },
          ),
        ),
      ),
    );
  }
}
