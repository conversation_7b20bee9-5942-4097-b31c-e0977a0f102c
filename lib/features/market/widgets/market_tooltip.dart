import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

class MarketTooltip extends StatelessWidget {
  final String label;
  final int value;
  final Color color;

  const MarketTooltip({
    super.key,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gh),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(4.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10.gsp,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          4.verticalSpace,
          Row(
            children: [
              Container(
                height: 5.gh,
                width: 5.gh,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              8.horizontalSpace,
              Text(
                '$value',
                style: TextStyle(
                  fontSize: 12.gsp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
