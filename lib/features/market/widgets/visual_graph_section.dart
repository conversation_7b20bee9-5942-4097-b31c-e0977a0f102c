import 'dart:math' show max, min;

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/time_range.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/models/route_arguments/trading_arguments.dart';
import '../../../shared/routes/routes.dart';
import '../../../shared/widgets/shimmer/shimmer_widget.dart';
import 'kline_graph.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class VisualGraphSection extends StatefulWidget {
  final bool isFromHome;

  const VisualGraphSection({super.key, this.isFromHome = false});

  @override
  State<VisualGraphSection> createState() => _VisualGraphSectionState();
}

class _VisualGraphSectionState extends State<VisualGraphSection> with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  int _firstVisibleItemIndex = 0;
  static const double _cardHeight = 170.0;
  static const double _graphHeight = 70.0;
  static const double _graphWidth = 120.0;
  final double _itemWidth = 0.32.gsw - 4.gw; // Exactly 1/3 of screen width
  final double _itemSpacing = 5.gw;

  // Variables for snapping behavior
  bool _isScrolling = false;
  int _targetPage = 0;
  final int _itemsPerPage = 3; // We want to show 3 cards per page
  late AnimationController _animationController;
  late Animation<double> _animation;
  final PageController _pageController = PageController(
    viewportFraction: 0.33,
    initialPage: 0,
  );

  // Calculate the total width of a single item including spacing
  double get _totalItemWidth => _itemWidth + _itemSpacing;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for smooth scrolling
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Add listeners for scroll events
    _scrollController.addListener(_onScroll);

    // Add listener for when scrolling ends to snap to the nearest page
    _scrollController.addListener(_onScrollEnd);

    // Add listener for when page changes
    _pageController.addListener(_onPageChanged);

    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        context.read<IndexTradeCubit>().updateAnimate();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.removeListener(_onScrollEnd);
    _scrollController.dispose();
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onPageChanged() {
    // Sync the PageController with our ScrollController
    if (_scrollController.hasClients && _scrollController.position.maxScrollExtent > 0) {
      _scrollController.position.notifyListeners();
    }
  }

  // This method handles the scrolling logic and updates the current visible index
  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final state = context.read<IndexTradeCubit>().state;
    final itemCount = state.indexStocks.length;
    if (itemCount == 0) return;

    final viewportWidth = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;

    // Calculate the current page based on scroll offset

    // Calculate the first visible item index
    final viewportCenter = scrollOffset + (viewportWidth / 2);
    final calculatedIndex = (viewportCenter / _totalItemWidth).floor();
    final safeIndex = max(0, min(calculatedIndex, itemCount - 1));

    if (safeIndex != _firstVisibleItemIndex) {
      setState(() => _firstVisibleItemIndex = safeIndex);
    }
  }

  // This method detects when scrolling ends and snaps to the nearest page
  void _onScrollEnd() {
    if (!_scrollController.hasClients) return;

    // Check if the scroll is idle (not being dragged)
    if (!_scrollController.position.isScrollingNotifier.value) {
      if (_isScrolling) {
        _isScrolling = false;
        _snapToPage();
      }
    } else {
      _isScrolling = true;
    }
  }

  // Snap to the nearest page when scrolling ends
  void _snapToPage() {
    if (!_scrollController.hasClients) return;

    final viewportWidth = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;

    // Calculate the target page based on current scroll position
    final itemCount = context.read<IndexTradeCubit>().state.indexStocks.length;
    final maxPages = (itemCount / _itemsPerPage).ceil();

    // Calculate which page we're closest to
    final page = (scrollOffset / viewportWidth).round();
    _targetPage = max(0, min(page, maxPages - 1));

    // Calculate the target offset for the page
    final targetOffset = _targetPage * viewportWidth;

    // Only animate if we're not already at the target
    if ((targetOffset - scrollOffset).abs() > 0.5) {
      // Set up the animation
      _animation = Tween<double>(
        begin: scrollOffset,
        end: targetOffset,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ));

      // Add listener to update scroll position during animation
      _animation.addListener(() {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(_animation.value);
        }
      });

      // Reset and start the animation
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<IndexTradeCubit, IndexTradeState>(
          listenWhen: (previous, current) =>
              previous.indexes[current.selectedIndex].lotSize != current.indexes[current.selectedIndex].lotSize ||
              previous.selectedIndex != current.selectedIndex,
          listener: (context, state) {
            if (widget.isFromHome) return;
            context.read<TradingCubit>()
              ..setOrderFraction(orderFraction: OrderFraction.full)
              ..setIndexTrading(isIndexTrading: true);
          },
        ),
        BlocListener<IndexTradeCubit, IndexTradeState>(
          listenWhen: (previous, current) => previous.animate != current.animate,
          listener: (context, state) {
            if (widget.isFromHome) return;
            if (state.animate) {
              _pageController.animateToPage(
                state.selectedIndex,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
              setState(() {
                _firstVisibleItemIndex = state.selectedIndex;
              });
            }
          },
        ),
      ],
      child: BlocBuilder<IndexTradeCubit, IndexTradeState>(
        builder: (context, state) {
          int itemCount = state.indexStocks.length;
          if (state.status == DataStatus.loading && itemCount == 0) {
            return _buildLoading();
          }

          if (state.status.isLoading) {
            itemCount = 6;
          }
          if (state.status.isFailed || itemCount == 0) {
            return const Center(child: TableEmptyWidget());
          }

          return Column(
            children: [
              Container(
                height: _cardHeight.gh,
                padding: EdgeInsets.symmetric(horizontal: 0, vertical: 5.gw),
                child: PageView.builder(
                  key: PageStorageKey('visual_graph//${widget.isFromHome}'),
                  controller: _pageController,
                  itemCount: itemCount,
                  onPageChanged: (index) {
                    setState(() {
                      _firstVisibleItemIndex = index;
                    });
                  },
                  pageSnapping: true,
                  padEnds: false,
                  // physics: const CustomPageViewScrollPhysics(),
                  itemBuilder: (context, i) => Padding(
                    padding: EdgeInsets.symmetric(horizontal: _itemSpacing / 2),
                    child: _buildStockCard(context, state, i),
                  ),
                ),
              ),
              if (itemCount > 0) _buildIndicators(itemCount - 2, context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStockCard(BuildContext context, IndexTradeState state, int i) {
    final data = state.indexStocks;
    final isSelected = i == state.selectedIndex;

    final market = getMainMarketType(data[i].stockInfo.data?.market ?? '');
    // Extract stock data
    final _IndexStockData stockData = _extractStockData(context, data, i, market);

    return GestureDetector(
      onTap: () => _handleCardTap(data, i),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: _itemWidth,
        padding: EdgeInsets.symmetric(vertical: 5.gh),
        margin: _calculateCardMargin(isSelected, i),
        decoration: _buildCardDecoration(context, isSelected),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          spacing: 4,
          children: [
            SizedBox(height: 3.gh),
            // Stock name
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.gw),
              child: Text(
                stockData.name,
                style: context.textTheme.primary.w700.copyWith(color: stockData.textColor, fontSize: 14.gsp),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Graph
            KlineGraph(
              spots: stockData.spots,
              textColor: stockData.textColor,
              lineColor: stockData.lineColor,
              width: _graphWidth,
              height: _graphHeight,
              market: market,
            ),
            SizedBox(height: 5.gw),
            // Price
            FlipText(
              stockData.price,
              style: TextStyle(
                fontSize: 14.gsp,
                fontWeight: FontWeight.w600,
                color: stockData.textColor,
              ),
            ),
            // Change and percentage
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              spacing: 4,
              children: [
                // Price change
                FlipText(
                  stockData.change,
                  prefix: stockData.change >= 0 ? '+' : '',
                  style: TextStyle(
                    fontSize: 11.gsp,
                    color: stockData.textColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                // Percentage change
                FlipText(
                  stockData.gainPercentage,
                  suffix: '%',
                  style: TextStyle(
                    fontSize: 11.gsp,
                    color: stockData.textColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Extract stock data from the state
  _IndexStockData _extractStockData(BuildContext context, List<IndexStockInfo> data, int index, MainMarketType market) {
    // Default values
    String name = '';
    double price = 0.0, gain = 0.00, change = 0.0;
    Color textColor = Colors.grey;
    List<FlSpot> spots = [];

    if (data.isNotEmpty && index < data.length) {
      final stockInfo = data[index].stockInfo.data;
      if (stockInfo != null) {
        name = stockInfo.name ?? '';
        price = stockInfo.latestPrice ?? 0.0;
        spots = _getSpots(data[index].klineData.data, market);
        change = price - (stockInfo.close ?? 0.0);

        // Avoid division by zero
        final closePrice = stockInfo.close ?? 0.0;
        gain = closePrice != 0.0 ? change / closePrice : 0.0;

        textColor = gain.getValueColor(context);
      }
    }

    return _IndexStockData(
      name: name,
      price: price,
      change: change,
      gainPercentage: gain * 100,
      textColor: textColor,
      lineColor: textColor,
      spots: spots,
    );
  }

  /// Calculate card margin based on selection state and position
  EdgeInsets _calculateCardMargin(bool isSelected, int index) {
    return EdgeInsets.only(
      right: isSelected && !widget.isFromHome ? 0.gw : 1.gw,
      left: index == 0 ? 4.gw : 1.gw,
      bottom: isSelected && !widget.isFromHome ? 0 : 2.gw,
      top: isSelected && !widget.isFromHome ? 0 : 2.gw,
    );
  }

  /// Build card decoration with proper styling
  BoxDecoration _buildCardDecoration(BuildContext context, bool isSelected) {
    return BoxDecoration(
      color: context.theme.cardColor,
      borderRadius: BorderRadius.circular(12),
      border: isSelected && !widget.isFromHome ? Border.all(color: Theme.of(context).primaryColor, width: 2.gw) : null,
      boxShadow: [
        BoxShadow(
          color: Colors.black.withNewOpacity(0.15),
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ],
    );
  }

  void _handleCardTap(List<IndexStockInfo> data, int i) {
    if (widget.isFromHome) {
      context.verifyAuth(
        () {
          StockInfoData? stockInfo = data[i].stockInfo.data;
          Navigator.pushNamed(
            context,
            routeTradingCenter,
            arguments: TradingArguments(
              instrumentInfo: stockInfo!.instrumentInfo,
              selectedIndex: TradeTabType.Quotes.index,
              shouldNavigateToIndex: widget.isFromHome,
              isIndexTrading: true,
            ),
          );
        },
      );
    }
    context.read<IndexTradeCubit>().updateSelectedIndex(i);
    if (!widget.isFromHome) {
      context.read<TradingCubit>()
        ..setSelectedPositionOpenShort(null)
        ..setSelectedPositionSellLong(null);
    }
  }

  Widget _buildIndicators(int itemCount, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          itemCount,
          (index) => AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: index == _firstVisibleItemIndex ? 12 : 6,
            height: 6,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(3),
              color: index == _firstVisibleItemIndex ? Theme.of(context).primaryColor : Colors.grey.withNewOpacity(0.3),
            ),
          ),
        ),
      ),
    );
  }

  List<FlSpot> _getSpots(StockKlineData? klineData, MainMarketType market) {
    final limit = TimeRange.shareMinutesPerDay(market);
    if (klineData?.list == null || klineData!.list!.isEmpty) return [];

    final list = klineData.list!;

    try {
      // Extract prices from kline data
      final klinePrices = list.map((item) {
        return item.price == 0.0 ? (item.open ?? 0.0) : (item.price ?? 0.0);
      }).toList();

      // Early return if empty
      if (klinePrices.isEmpty) return [];

      final paddedPrices = List.generate(limit, (index) => index < klinePrices.length ? klinePrices[index] : null);

      // Get min and max values for normalization
      final validPrices = paddedPrices.whereType<double>().toList();

      // Check if valid prices exist
      if (validPrices.isEmpty) return [];

      final minValue = validPrices.reduce(min);
      final maxValue = validPrices.reduce(max);
      final range = maxValue - minValue;

      // If range is zero, return flat line
      if (range == 0) {
        return klinePrices
            .asMap()
            .entries
            .map(
              (entry) => FlSpot(entry.key.toDouble(), 5.0), // Middle value
            )
            .toList();
      }

      // Normalize prices to 0-10 range
      return paddedPrices.asMap().entries.map((entry) {
        if (entry.value == null) return FlSpot.nullSpot;
        return FlSpot(entry.key.toDouble(), ((entry.value! - minValue) / range * 10).toDouble());
      }).toList();
    } catch (e) {
      return [];
    }
  }

  Widget _buildLoading() {
    final double itemWidth = 0.32.gsw - 4.gw;
    return SizedBox(
      height: 180.gh,
      child: ListView.separated(
        itemCount: 3,
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 6),
        scrollDirection: Axis.horizontal,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (_, __) => ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: ShimmerWidget(
            height: 190.gh,
            width: itemWidth,
          ),
        ),
      ),
    );
  }
}

/// Helper class to store stock card data
class _IndexStockData {
  final String name;
  final double price;
  final double change;
  final double gainPercentage;
  final Color textColor;
  final Color lineColor;
  final List<FlSpot> spots;

  _IndexStockData({
    required this.name,
    required this.price,
    required this.change,
    required this.gainPercentage,
    required this.textColor,
    required this.lineColor,
    required this.spots,
  });
}
