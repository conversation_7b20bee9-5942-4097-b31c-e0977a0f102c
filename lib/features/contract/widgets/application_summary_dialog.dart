import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/functions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';

import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/widgets/alert_dilaog/custom_alert_dialog.dart';
import '../logic/contract/contract_cubit.dart';

class ApplicationSummaryDialog extends StatelessWidget {
  final MainContractType mainContractType;
  final ContractType contractType;

  const ApplicationSummaryDialog({super.key, required this.mainContractType, required this.contractType});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: CustomAlertDialog(
        hideActionButton: true,
        child: BlocBuilder<ContractCubit, ContractState>(
          builder: (context, state) {
            return SizedBox(
              height: 0.5.gsh,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    '${'applyFor'.tr()}  [${contractType.title(mainContractType).tr()}]',
                    style: context.textTheme.primary.fs16.w500,
                  ),
                  16.verticalSpace,
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        spacing: 10.gh,
                        children: [
                          AmountRow(
                            title: 'contractType'.tr(),
                            value: mainContractType == MainContractType.futures
                                ? 'futures.futuresFunding'.tr()
                                : contractMarketTranslation[state.selectedMarket?.market ?? '']?.tr() ?? '',
                          ),
                          AmountRow(
                            title: 'period'.tr(),
                            value: getPeriodType(state.selectedContractConfig?.periodType ?? 0),
                          ),
                          AmountRow(
                            title: 'contractMultiple'.tr(),
                            value: '${state.selectedConfigList?.multiple.toString() ?? ''}倍',
                          ),
                          if (contractType != ContractType.bonus) ...[
                            AmountRow(
                              title: 'contractMargin'.tr(),
                              amount: state.selectedAmount ?? 0,
                              suffix: state.currency,
                            ),
                          ],
                          AmountRow(
                            title: 'totalMargin'.tr(),
                            amount: state.contractModelAmount?.totalTadingFunds ?? 0,
                            suffix: state.currency,
                          ),
                          AmountRow(
                            title: 'warningLine'.tr(),
                            amount: state.contractModelAmount?.lossWarningLine ?? 0,
                            suffix: state.currency,
                          ),
                          AmountRow(
                            title: 'stopLossLine'.tr(),
                            amount: state.contractModelAmount?.lossFlatLine ?? 0,
                            suffix: state.currency,
                          ),
                          AmountRow(
                            title: 'interestRate'.tr(),
                            amount: state.contractCalculation?.rateAmount ?? 0,
                            currency:
                                '${state.currency} / ${contractTypeTranslation[state.selectedContractConfig?.periodType ?? 0]?.$2.tr()}',
                          ),
                          AmountRow(
                            title: 'interestReduceAmount'.tr(),
                            amount: state.contractCalculation?.deductInterestCashCNY ?? 0,
                            isCurrency: true,
                            suffix: 'CNY',
                          ),
                          AmountRow(
                            title: 'actualPaymentAmount'.tr(),
                            amount: state.contractCalculation?.deductCanUseCashCNY ?? 0,
                            isCurrency: true,
                            prefix: state.currency != 'CNY' ? '≈ ' : '',
                            currency: 'CNY',
                          ),
                          16.verticalSpace,
                        ],
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: CommonButton(
                          title: 'cancel'.tr(),
                          style: CommonButtonStyle.outlined,
                          onPressed: () => Navigator.pop(context),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      12.horizontalSpace,
                      Expanded(
                        child: BlocConsumer<ContractCubit, ContractState>(
                          listener: (context, state) {
                            if (state.applyOrdinaryContractStatus == DataStatus.success) {
                              context.read<AccountCubit>().getContractSummary();
                              _showSuccessDialog(context);
                            } else if (state.applyOrdinaryContractStatus == DataStatus.failed) {
                              GPEasyLoading.showToast(state.error ?? 'somethingWentWrong'.tr());
                            }
                          },
                          builder: (context, state) {
                            return CommonButton(
                              showLoading: state.applyOrdinaryContractStatus.isLoading,
                              title: 'submit'.tr(),
                              style: CommonButtonStyle.primary,
                              onPressed: () =>
                                  context.read<ContractCubit>().applyOrdinaryContract(contractType: mainContractType),
                            );
                          },
                        ),
                      ),
                    ],
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _showSuccessDialog(BuildContext context) {
    GPEasyLoading.showToast('contractSubmittedSuccessfully'.tr());
    Navigator.popUntil(context, (route) => route.isFirst);
  }
}
