import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

class SelectableButton extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const SelectableButton({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: AspectRatio(
        aspectRatio: 2.3,
        child: Container(
          decoration: BoxDecoration(
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      context.theme.primaryColorLight,
                      context.theme.primaryColor,
                    ],
                  )
                : null,
            borderRadius: BorderRadius.circular(6.gr),
            border: Border.all(color: isSelected ? Colors.transparent : context.colorTheme.textRegular),
          ),
          child: Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.gw),
              child: AutoSizeText(
                title,
                minFontSize: 12,
                style: context.textTheme.secondary.fs14.copyWith(
                  color: isSelected ? context.colorTheme.buttonPrimary : context.colorTheme.textRegular,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
