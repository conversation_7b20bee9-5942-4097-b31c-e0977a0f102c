import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';

import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/widgets/alert_dilaog/custom_alert_dialog.dart';
import '../logic/contract_activity/contract_activity_cubit.dart';

/// Dialog for showing contract activity summary for
///  [ContractType.experience], [ContractType.bonus]
class ActivitySummaryDialog extends StatelessWidget {
  final ContractType contractType;
  final MainContractType mainContractType;
  const ActivitySummaryDialog({super.key, required this.contractType, required this.mainContractType});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: CustomAlertDialog(
        hideActionButton: true,
        child: SizedBox(
          height: contractType == ContractType.bonus ? 420.gh : 300.gh,
          child: BlocBuilder<ContractActivityCubit, ContractActivityState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    '${'applyFor'.tr()} [${contractType.title(mainContractType).tr()}]',
                    style: context.textTheme.primary.fs16.w500,
                  ),
                  16.verticalSpace,
                  AmountRow(
                    title: 'contractType'.tr(),
                    value: contractMarketTranslation[state.selectedMarket?.marketType ?? '']?.tr() ?? '',
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: 'period'.tr(),
                    value: 'contract.period_1'.tr(),
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: 'contractMultiple'.tr(),
                    value: '${state.selectedMarket?.multiple.toString() ?? ''}${'xTimes'.tr()}',
                  ),
                  10.verticalSpace,
                  if (contractType == ContractType.experience) ...[
                    AmountRow(
                      title: 'giftAmount'.tr(),
                      amount: state.selectedAmount,
                      suffix: state.currency,
                    ),
                    10.verticalSpace,
                  ],
                  if (contractType == ContractType.bonus) ...[
                    AmountRow(
                      title: 'giftAmount'.tr(),
                      amount: state.selectedMarket?.shareRatio ?? 0,
                      suffix: state.currency,
                    ),
                    10.verticalSpace,
                    AmountRow(
                      title: 'contractMargin'.tr(),
                      amount: state.selectedAmount,
                      suffix: state.currency,
                    ),
                    10.verticalSpace,
                  ],
                  AmountRow(
                    title: 'totalMargin'.tr(),
                    amount: state.contractModelAmount?.totalTadingFunds ?? 0,
                    suffix: state.currency,
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: 'lossWarningLine'.tr(),
                    amount: state.contractModelAmount?.lossWarningLine ?? 0,
                    suffix: state.currency,
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: 'liquidationLine'.tr(),
                    amount: state.contractModelAmount?.lossFlatLine ?? 0,
                    suffix: state.currency,
                  ),
                  if (contractType == ContractType.bonus) ...[
                    10.verticalSpace,
                    AmountRow(
                      title: 'capitalInterestRate'.tr(),
                      amount: state.bonusContractCalculation?.rateAmount ?? 0,
                      currency: '${state.currency}/${'day'.tr()}',
                    ),
                    10.verticalSpace,
                    AmountRow(
                      title: 'interestReduceAmount'.tr(),
                      amount: state.bonusContractCalculation?.deductInterestCashCNY ?? 0,
                      suffix: state.contractActivity?.currency ?? '',
                    ),
                    10.verticalSpace,
                    AmountRow(
                      title: 'actualPaymentAmount2'.tr(),
                      amount: state.bonusContractCalculation?.deductCanUseCashCNY ?? 0,
                      prefix: state.currency != 'CNY' ? '≈ ' : '',
                      currency: 'CNY',
                    ),
                  ],
                  16.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: CommonButton(
                          style: CommonButtonStyle.outlined,
                          onPressed: () => Navigator.pop(context),
                          title: 'cancel'.tr(),
                          radius: 6.gr,
                        ),
                      ),
                      12.horizontalSpace,
                      Expanded(
                        child: MultiBlocListener(
                          listeners: [
                            BlocListener<ContractActivityCubit, ContractActivityState>(
                              listenWhen: (previous, current) =>
                                  previous.applyExperienceContractStatus != current.applyExperienceContractStatus ||
                                  previous.applyBonusContractStatus != current.applyBonusContractStatus,
                              listener: (context, state) {
                                if (state.applyExperienceContractStatus == DataStatus.success ||
                                    state.applyBonusContractStatus == DataStatus.success) {
                                  _navigation(context);
                                } else if (state.applyExperienceContractStatus == DataStatus.failed ||
                                    state.applyBonusContractStatus == DataStatus.failed) {
                                  GPEasyLoading.showToast(state.error);
                                }
                              },
                            ),
                          ],
                          child: CommonButton(
                            showLoading: state.applyExperienceContractStatus == DataStatus.loading ||
                                state.applyBonusContractStatus == DataStatus.loading,
                            radius: 6.gr,
                            onPressed: () => switch (contractType) {
                              ContractType.experience =>
                                context.read<ContractActivityCubit>().applyExperienceContract(),
                              ContractType.bonus => context.read<ContractActivityCubit>().applyBonusContract(),
                              _ => null,
                            },
                            title: 'submit'.tr(),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  void _navigation(BuildContext context) {
    context.read<AccountCubit>().getContractSummary();
    GPEasyLoading.showToast('contractSubmittedSuccessfully'.tr());
    Navigator.popUntil(context, (route) => route.isFirst);
  }
}
