import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

import '../../../shared/theme/font_pallette.dart';

class AmountRow extends StatelessWidget {
  final String title;
  final num? amount;
  final String? currency;
  final String? value;
  final bool isLeft;
  final double? fontSize;
  final Color? color;
  final String? suffix;
  final String? prefix;
  final int? fractionDigits;
  final bool isCurrency;
  final bool showTotalToolTip;
  const AmountRow({
    super.key,
    required this.title,
    this.amount,
    this.currency,
    this.isLeft = false,
    this.value,
    this.fontSize,
    this.color,
    this.suffix,
    this.prefix,
    this.fractionDigits,
    this.isCurrency = false,
    this.showTotalToolTip = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: isLeft ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
      children: [
        Row(
          spacing: 10,
          children: [
            Text(
              title,
              style: context.textTheme.regular,
            ),
            if (showTotalToolTip)
              GestureDetector(
                onTap: () => showToolTip(context),
                child: Icon(
                  Icons.help_outline,
                  color: context.theme.primaryColor,
                  size: fontSize ?? 14.gsp,
                ),
              ),
          ],
        ),
        if (isLeft) 12.horizontalSpace,
        (value != null)
            ? Text(
                value!,
                style: context.textTheme.regular.w800.ffAkz.copyWith(
                  color: color ?? context.colorTheme.textPrimary,
                ),
              )
            : FlipText(
                amount!.toDouble(),
                isCurrency: isCurrency,
                fractionDigits: fractionDigits ?? 2,
                style: context.textTheme.primary.w800.ffAkz.copyWith(
                  color: color ?? context.colorTheme.textPrimary,
                ),
                suffix: ' ${suffix ?? (currency == null ? '' : '$currency')}',
                prefix: prefix,
              )
      ],
    );
  }
}

void showToolTip(BuildContext context) {
  showModalBottomSheet(
    context: context,
    showDragHandle: true,
    builder: (context) => Container(
      padding: EdgeInsets.only(left: 16.gh, right: 16.gw, bottom: 30.gh, top: 0.gh),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'totalTip'.tr(),
            style: FontPalette.normal14.copyWith(
              color: context.colorTheme.textPrimary,
            ),
          ),
        ],
      ),
    ),
  );
}
