import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/contract/logic/contract/contract_cubit.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../shared/constants/assets.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/routes/routes.dart';

class ContractApplyScreen extends StatelessWidget {
  const ContractApplyScreen({super.key, required this.mainContractType});

  final MainContractType mainContractType;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(
          color: context.colorTheme.textPrimary,
        ),
        title: Text(
          mainContractType.translationKey.tr(),
          style: FontPalette.medium16,
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.description_outlined,
            ),
            onPressed: () => Navigator.pushNamed(context, routeContractApplyRecord),
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        child: AnimationLimiter(
          child: BlocSelector<ContractCubit, ContractState,
              (DataStatus openContractTypeFetchStatus, List<int>? openContractTypes)>(
            selector: (state) => (state.openContractTypeFetchStatus, state.openContractTypes),
            builder: (context, state) {
              if (state.$1.isLoading) {
                return Column(
                  children: List.generate(
                    3,
                    (index) => _buildShimmerLoadingItem(context),
                  ),
                );
              }
              if (state.$1 == DataStatus.failed) {
                return TableEmptyWidget();
              }
              return Column(
                spacing: 10.gh,
                children: AnimationConfiguration.toStaggeredList(
                  duration: const Duration(milliseconds: 600),
                  childAnimationBuilder: (widget) => SlideAnimation(
                    verticalOffset: 50.0,
                    child: FadeInAnimation(
                      child: widget,
                    ),
                  ),
                  children: [
                    if (state.$2?.contains(0) ?? false)
                      _ContractOption(
                        icon: Assets.contractCommonIcon,
                        mainContractType: mainContractType,
                        contractType: ContractType.standard,
                        onTap: () => Navigator.pushNamed(context, routeContractApplication, arguments: {
                          'mainContractType': mainContractType,
                          'contractType': ContractType.standard,
                        }),
                      ),
                    if (state.$2?.contains(2) ?? false)
                      _ContractOption(
                        icon: Assets.contractExpIcon,
                        mainContractType: mainContractType,
                        contractType: ContractType.experience,
                        onTap: () => Navigator.pushNamed(context, routeContractActivity, arguments: {
                          'mainContractType': mainContractType,
                          'contractType': ContractType.experience,
                        }),
                      ),
                    if (state.$2?.contains(1) ?? false)
                      _ContractOption(
                        icon: Assets.contractLottIcon,
                        mainContractType: mainContractType,
                        contractType: ContractType.bonus,
                        onTap: () => Navigator.pushNamed(context, routeContractActivity, arguments: {
                          'mainContractType': mainContractType,
                          'contractType': ContractType.bonus,
                        }),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerLoadingItem(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 10.gh),
      child: ShimmerWidget(
        child: Container(
          width: double.infinity,
          height: 64.gh,
          decoration: BoxDecoration(
            color: context.colorTheme.stockGreen,
            borderRadius: BorderRadius.circular(5.gr),
          ),
        ),
      ),
    );
  }
}

class _ContractOption extends StatelessWidget {
  final String icon;
  final VoidCallback onTap;
  final MainContractType mainContractType;
  final ContractType contractType;
  const _ContractOption({
    required this.icon,
    required this.onTap,
    required this.mainContractType,
    required this.contractType,
  });

  @override
  Widget build(BuildContext context) {
    String title() => switch (mainContractType) {
          MainContractType.stock => switch (contractType) {
              ContractType.standard => 'normalContract',
              ContractType.experience => 'experienceContract',
              ContractType.bonus => 'bonusContract',
            },
          MainContractType.futures => switch (contractType) {
              ContractType.standard => 'futures.types.normalFutures',
              ContractType.experience => 'futures.types.experienceFutures',
              ContractType.bonus => 'futures.types.bonusFutures',
            },
        };
    return AnimationConfiguration.synchronized(
      duration: const Duration(milliseconds: 300),
      child: ScaleAnimation(
        scale: 0.95,
        child: Bounceable(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            height: 64.gh,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  context.theme.primaryColorLight,
                  context.theme.primaryColor,
                ],
              ),
              borderRadius: BorderRadius.circular(5.gr),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.gw),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title().tr(),
                    style: context.textTheme.secondary.fs16.w500,
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: context.colorTheme.buttonPrimary,
                    size: 24.gr,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
