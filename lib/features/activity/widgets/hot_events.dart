import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/models/home_notification/home_notification_model.dart';
import 'package:gp_stock_app/features/home/<USER>/home_notification_cubit/home_notification_cubit.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:shimmer/shimmer.dart';

import '../../../shared/routes/routes.dart';

class HotEvents extends StatefulWidget {
  const HotEvents({super.key});

  @override
  State<HotEvents> createState() => _HotEventsState();
}

class _HotEventsState extends State<HotEvents> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeNotificationCubit, List<HomeNotificationModel>>(
      builder: (context, state) {
        // Filter notifications by type
        final notifications = state.where((e) => e.type == 1).toList();

        if (state.isEmpty) {
          return const HotEventsShimmer();
        }

        return AnimationLimiter(
          child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (context, index) => SizedBox(height: 10.gh),
            itemCount: notifications.length,
            shrinkWrap: true,
            itemBuilder: (context, index) {
              return AnimationConfiguration.staggeredList(
                position: index,
                duration: const Duration(milliseconds: 600),
                child: SlideAnimation(
                  verticalOffset: 30.0,
                  child: FadeInAnimation(
                    child: _buildNotificationItem(context, notifications[index]),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  // Extracted widget for individual notification item
  Widget _buildNotificationItem(BuildContext context, HomeNotificationModel notification) {
    return GestureDetector(
      onTap: () => Navigator.pushNamed(context, routeEventDetails, arguments: notification),
      child: Container(
        color: context.theme.cardColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImageSection(notification.imageUrl),
            SizedBox(height: 10.gh),
            _buildContentSection(context, notification),
          ],
        ),
      ),
    );
  }

  // Extracted widget for the image section
  Widget _buildImageSection(String imageUrl) {
    return Hero(
      tag: imageUrl,
      child: Container(
        width: 347.gw,
        height: 126.gh,
        decoration: BoxDecoration(
          color: context.theme.primaryColor.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12.gr),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.gr),
            topRight: Radius.circular(12.gr),
          ),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.cover,
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          ),
        ),
      ),
    );
  }

  // Extracted widget for the content section
  Widget _buildContentSection(BuildContext context, HomeNotificationModel notification) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(12.gr),
          bottomRight: Radius.circular(12.gr),
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 12.gh),
      child: Text(
        notification.title,
        style: FontPalette.normal14,
      ),
    );
  }
}

class HotEventsShimmer extends StatelessWidget {
  final int itemCount;

  const HotEventsShimmer({
    super.key,
    this.itemCount = 3,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: ListView.separated(
        separatorBuilder: (context, index) => SizedBox(height: 10.gh),
        itemCount: itemCount,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Shimmer for Image
              Container(
                width: 347.gw,
                height: 126.gh,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.gr),
                ),
              ),
              SizedBox(height: 10.gh),
              // Shimmer for Title
              Container(
                width: 200.gw,
                height: 16.gh,
                color: Colors.white,
                margin: EdgeInsets.symmetric(horizontal: 12.gh),
              ),
            ],
          );
        },
      ),
    );
  }
}
