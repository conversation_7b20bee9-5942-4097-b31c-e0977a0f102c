// import 'package:flutter/material.dart';
// import 'package:flutter_bounceable/flutter_bounceable.dart';
// import 'package:gp_stock_app/core/utils/convert_helper.dart';
// import 'package:gp_stock_app/core/utils/extensions.dart';
// import 'package:gp_stock_app/core/utils/screen_util.dart';
// import 'package:gp_stock_app/features/home/<USER>/models/home_model.dart';
// import 'package:gp_stock_app/shared/routes/routes.dart';
// import 'package:gp_stock_app/shared/theme/color_pallette.dart';

// import '../../../shared/theme/font_pallette.dart';
// import '../../../shared/theme/my_color_scheme.dart';
// import '../../../shared/widgets/shimmer/shimmer_widget.dart';

// class News extends StatelessWidget {
//   const News({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return ListView.separated(
//       physics: const NeverScrollableScrollPhysics(),
//       itemCount: 5,
//       shrinkWrap: true,
//       separatorBuilder: (_, __) => Divider(
//         height: 24.gh,
//         thickness: 1,
//         color: ColorPalette.primaryColor.withNewOpacity(0.05),
//       ),
//       itemBuilder: (context, index) {
//         final news = NewsModel(
//           id: index,
//           title: '出现松动？美对中国光伏或"部分撤税"，光伏板块是否会迎来拐点？',
//           source: '我是新闻内容我是新闻内容我是新闻内容我是新闻内容我是新闻内容我是新闻内容我是新闻内容我是新闻内容',
//           coverUrl: 'https://picsum.photos/200/300?random=$index',
//           createTime: DateTime.now().toString(),
//         );
//         // if (state.newsUpdatesFetchStatus == DataStatus.loading &&
//         //     state.newsDataList?.data.newsDataList.isEmpty == true) {
//         //   return const NewsListItemShimmer();
//         // }
//         return Bounceable(
//           onTap: () => Navigator.pushNamed(context, routeNewsDetails),//todo
//           child: Row(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               _NewsImage(
//                 imageUrl: news.coverUrl,
//               ),
//               16.horizontalSpace,
//               _NewsContent(
//                 title: news.title,
//                 source: news.source ?? '',
//                 datetime: ConvertHelper.formatDateGeneral(
//                   news.createTime,
//                 ),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }
// }

// class _NewsContent extends StatelessWidget {
//   final String title;
//   final String source;
//   final String datetime;

//   const _NewsContent({
//     required this.title,
//     required this.source,
//     required this.datetime,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       width: 190.gw,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             title,
//             maxLines: 1,
//             style: FontPalette.normal15.copyWith(
//               color: context.colorTheme.primary,
//               height: 1.2,
//             ),
//             overflow: TextOverflow.ellipsis,
//           ),
//           8.verticalSpace,
//           DefaultTextStyle(
//             style: FontPalette.semiBold9.copyWith(
//               color: context.appTheme.newsSourceColor,
//             ),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   datetime,
//                   style: FontPalette.normal13.copyWith(
//                     color: context.appTheme.newsSourceColor,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           5.verticalSpace,
//           Text(
//             source,
//             style: FontPalette.normal10.copyWith(
//               color: context.appTheme.newsSourceColor,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// class _NewsImage extends StatelessWidget {
//   final String imageUrl;

//   const _NewsImage({required this.imageUrl});

//   @override
//   Widget build(BuildContext context) {
//     return ClipRRect(
//       borderRadius: BorderRadius.circular(8.gr),
//       child: SizedBox(
//         width: 100.gw,
//         height: 100.gh,
//         child: Image.network(
//           imageUrl,
//           fit: BoxFit.cover,
//           loadingBuilder: (_, child, loadingProgress) {
//             if (loadingProgress == null) return child;
//             return const _NewsImageShimmer();
//           },
//           errorBuilder: (_, __, ___) => const _ErrorImage(),
//         ),
//       ),
//     );
//   }
// }

// class _NewsImageShimmer extends StatelessWidget {
//   const _NewsImageShimmer();

//   @override
//   Widget build(BuildContext context) {
//     return ShimmerWidget(
//       width: 120.gw,
//       height: 80.gh,
//     );
//   }
// }

// class _ErrorImage extends StatelessWidget {
//   const _ErrorImage();

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       width: 120.gw,
//       height: 80.gh,
//       decoration: BoxDecoration(
//         color: ColorPalette.primaryColor.withNewOpacity(0.05),
//         borderRadius: BorderRadius.circular(8.gr),
//       ),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Icon(
//             Icons.image_not_supported_rounded,
//             color: ColorPalette.primaryColor.withNewOpacity(0.3),
//             size: 24.gw,
//           ),
//           4.verticalSpace,
//           Text(
//             'Image not available',
//             style: FontPalette.semiBold9.copyWith(
//               color: ColorPalette.primaryColor.withNewOpacity(0.3),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
