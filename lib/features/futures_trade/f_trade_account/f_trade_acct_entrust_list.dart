import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account/widgets/table_loading.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/list_view_cell/account_entrust_cell.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FTradeAcctEntrustList extends StatefulWidget {
  final DataStatus dataStatus;
  final FTradeAcctOrderModel? orderModel;
  final RefreshController refreshCtrl;

  /// actionType => 'tap' 'cancle'
  final Function(String actionType, FTradeAcctOrderRecords record) onUserActions;
  const FTradeAcctEntrustList({
    super.key,
    required this.dataStatus,
    required this.orderModel,
    required this.refreshCtrl,
    required this.onUserActions,
  });

  @override
  State<FTradeAcctEntrustList> createState() => _FTradeAcctEntrustListState();
}

class _FTradeAcctEntrustListState extends State<FTradeAcctEntrustList> {
  @override
  Widget build(BuildContext context) {
    final headerTitles = [
      '${'name'.tr()} |${'code'.tr()}',
      'order_price'.tr(),
      '${'completed'.tr()}|${'total'.tr()}',
      '${'direction'.tr()}|${'status'.tr()}',
      ('operate'.tr()),
    ];
    final flexValues = [3, 2, 2, 2, 2];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 12.gh),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(10.gr),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              for (var i = 0; i < headerTitles.length; i++) ...[
                if (i > 0 && i < 5) 5.horizontalSpace,
                Expanded(
                  flex: flexValues[i],
                  child: Tooltip(
                    message: headerTitles[i],
                    child: Text(
                      headerTitles[i],
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      textAlign: i == 0 ? TextAlign.left : TextAlign.center,
                      style: context.textTheme.regular.fs12,
                    ),
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: 10),
          _buildContent(context),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final status = widget.dataStatus;
    final orderModel = widget.orderModel;

    if (status == DataStatus.loading) {
      return const TableLoadingState();
    }

    if (orderModel == null || orderModel.records.isEmpty) {
      return const TableEmptyWidget();
    }

    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.only(bottom: 0.gh),
      itemCount: orderModel.records.length,
      separatorBuilder: (_, __) => Divider(
        color: context.theme.dividerColor,
        height: 1,
      ),
      itemBuilder: (_, index) {
        return AccountEntrustCell(
          data: orderModel.records[index],
          onTap: () => widget.onUserActions('tap', orderModel.records[index]),
          onTapCancelBtn: () => widget.onUserActions('cancle', orderModel.records[index]),
        );
      },
    );
  }
}
