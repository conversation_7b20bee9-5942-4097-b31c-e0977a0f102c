import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_all_info_screen.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FTradeAllInfoTitleView extends StatelessWidget {
  final int selectedIndex;
  final Function(int) selectTab;
  final Widget? widget;
  const FTradeAllInfoTitleView({
    super.key,
    required this.selectedIndex,
    required this.selectTab,
    this.widget,
  });

  @override
  Widget build(BuildContext context) => Container(
        height: 30.gh,
        width: 180.gw,
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          border: Border.all(color: context.theme.primaryColor),
          borderRadius: BorderRadius.circular(5.0),
        ),
        child: Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => selectTab(0),
                child: Container(
                  alignment: Alignment.center,
                  decoration: selectedIndex == 0 ? BoxDecoration(color: context.theme.primaryColor) : null,
                  child: Text(
                    FTradeAllInfoTitlesType.trade.name.tr(),
                    style: selectedIndex == 0
                        ? context.textTheme.buttonPrimary.fs15.w600
                        : context.textTheme.regular.fs15.w600,
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => selectTab(1),
                child: Container(
                  alignment: Alignment.center,
                  decoration: selectedIndex == 1
                      ? BoxDecoration(
                          color: context.theme.primaryColor,
                        )
                      : null,
                  child: Text(
                    FTradeAllInfoTitlesType.quotation.name.tr(),
                    style: selectedIndex == 1
                        ? context.textTheme.buttonPrimary.fs15.w600
                        : context.textTheme.regular.fs15.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
}
