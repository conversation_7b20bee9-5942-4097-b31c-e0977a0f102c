import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FTradeProfileScrollView extends StatefulWidget {
  final FTradeMarketType type;
  final FTradeListItemModel data;
  final List<({String label, String value, Color? valueColor})> displayList;

  const FTradeProfileScrollView({
    super.key,
    required this.type,
    required this.data,
    required this.displayList,
  });

  @override
  State<FTradeProfileScrollView> createState() => _FTradeProfileScrollViewState();
}

class _FTradeProfileScrollViewState extends State<FTradeProfileScrollView> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 14),
      child: CustomScrollView(
        physics: ClampingScrollPhysics(),
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: _SliverHeaderDelegate(
              minHeight: 42,
              maxHeight: 42,
              child: _buildHeaderTop(),
            ),
          ),
          SliverToBoxAdapter(child: _buildContent()),
        ],
      ),
    );
  }

  Widget _buildHeaderTop() {
    return Row(
      children: [
        SizedBox(width: 16),
        Text(
          widget.data.name,
          style: context.textTheme.primary,
        ),
        const SizedBox(width: 8),
        Container(
          height: 15.gh,
          padding: EdgeInsets.symmetric(horizontal: 4.gh, vertical: 2.gh),
          decoration: BoxDecoration(
            color: Color(0xffE9F0FD),
            borderRadius: BorderRadius.circular(2.gh),
          ),
          child: Text(
            widget.type.stringIdForCloud(),
            style: context.textTheme.primary.fs8,
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 14.gh),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(10.gr),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                "variety_information".tr(),
                style: context.textTheme.primary.fs16,
              ),
              Spacer(),
            ],
          ),
          10.verticalSpace,
          ...widget.displayList.take(7).map((item) {
            return _BuildRow(
              label: item.label,
              value: item.value,
              valueColor: item.valueColor,
            );
          }),
          SizedBox(height: 8),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 10.gh),
            decoration: BoxDecoration(
              color: context.theme.scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(10.gr),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      "margin_calculation".tr(),
                      style: context.textTheme.primary,
                    ),
                    Spacer(),
                  ],
                ),
                ...widget.displayList.skip(7).map((item) {
                  return _BuildRow(
                    label: item.label,
                    value: item.value,
                    valueColor: item.valueColor,
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _BuildRow extends StatelessWidget {
  final String label;
  final String value;
  final Color? valueColor;
  final String? currency;

  const _BuildRow({
    required this.label,
    required this.value,
    this.valueColor,
    this.currency,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            spacing: 10,
            children: [
              if (label == 'margin_per_lot'.tr())
                Text(
                  label,
                  style: context.textTheme.primary,
                ),
              if (label != 'margin_per_lot'.tr())
                Text(
                  label,
                  style: context.textTheme.regular,
                ),
            ],
          ),
          Text(
            value,
            style: context.textTheme.primary,
          ),
        ],
      ),
    );
  }
}

class _SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
