import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../shared/constants/enums.dart';
import '../logic/notifications/notifications_cubit.dart';
import '../widgets/notifications_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class NotificationListScreen extends StatefulWidget {
  const NotificationListScreen({super.key});

  @override
  State<NotificationListScreen> createState() => _NotificationListScreenState();
}

class _NotificationListScreenState extends State<NotificationListScreen> {
  int _selectedTabIndex = 0;
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _fetchNotifications();
    _setupScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _setupScrollController() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8 && !_isLoadingMore) {
        _loadMoreData();
      }
    });
  }

  Future<void> _loadMoreData() async {
    final state = context.read<NotificationsCubit>().state;
    final hasMorePages = (state.notificationData?.current ?? 0) < (state.notificationData?.pages ?? 0);

    if (hasMorePages && !_isLoadingMore) {
      setState(() => _isLoadingMore = true);

      await context.read<NotificationsCubit>().getNotificationList(
            messageType: _getMessageType(),
            isLoadMore: true,
          );

      setState(() => _isLoadingMore = false);
    }
  }

  void _fetchNotifications() {
    final type = _getMessageType();
    context.read<NotificationsCubit>()
      ..getNotificationList(messageType: type)
      ..getNotificationCount();
  }

  String _getMessageType() {
    switch (_selectedTabIndex) {
      case 0:
        return NotificationType.system.name;
      case 1:
        return NotificationType.warning.name;
      default:
        return NotificationType.system.name;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('myNotifications'.tr()),
      ),
      body: Column(
        children: [
          _buildTabs(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                _fetchNotifications();
              },
              child: _buildRegularNotificationsTab(),
            ),
          ),
        ],
      ),
    );
  }

  // Shows both regular and home notifications in the same list
  // Widget _buildCombinedNotificationsList() {
  //   return BlocBuilder<NotificationsCubit, NotificationsState>(
  //     builder: (context, notificationState) {
  //       if (notificationState.notificationFetchStatus == DataStatus.loading && !_isLoadingMore) {
  //         return _loadingState();
  //       }

  //       return BlocBuilder<HomeNotificationCubit, List<HomeNotificationModel>>(
  //         builder: (context, homeNotifications) {
  //           // Get popup notifications
  //           final popupNotifications = homeNotifications.where((e) => e.type == 3).toList();
  //           final regularNotifications = notificationState.notificationData?.records ?? [];

  //           if (regularNotifications.isEmpty && popupNotifications.isEmpty) {
  //             return Center(
  //               child: TableEmptyWidget(
  //                 width: 60.gw,
  //                 height: 60.gh,
  //               ),
  //             );
  //           }

  //           return ListView.builder(
  //             controller: _scrollController,
  //             itemCount: popupNotifications.length + regularNotifications.length + (_isLoadingMore ? 1 : 0),
  //             padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 8.gh),
  //             itemBuilder: (context, index) {
  //               if (index == popupNotifications.length + regularNotifications.length) {
  //                 return Padding(
  //                   padding: EdgeInsets.all(8.gr),
  //                   child: Center(
  //                     child: CircularProgressIndicator(),
  //                   ),
  //                 );
  //               }

  //               if (index < popupNotifications.length) {
  //                 final homeNotification = popupNotifications[index];
  //                 return _buildNotificationDetails(context, homeNotification);
  //               } else {
  //                 final regularIndex = index - popupNotifications.length;
  //                 final record = regularNotifications[regularIndex];
  //                 return NotificationItem(
  //                   isRead: record.status == 1,
  //                   icon: _getIcon(record.type ?? ''),
  //                   title: record.title ?? '',
  //                   subtitle: record.content ?? '',
  //                   color: _getColor(record.type ?? ''),
  //                   onTap: () async {
  //                     await context.read<NotificationsCubit>().readNotification(messageId: record.id ?? 0);
  //                     context.read<NotificationsCubit>().getNotificationCount();
  //                   },
  //                 );
  //               }
  //             },
  //           );
  //         },
  //       );
  //     },
  //   );
  // }

  Widget _buildRegularNotificationsTab() {
    return BlocBuilder<NotificationsCubit, NotificationsState>(
      builder: (context, state) {
        if (state.notificationFetchStatus == DataStatus.loading && !_isLoadingMore) {
          return _loadingState();
        }
        return _successState(state);
      },
    );
  }

  Widget _buildTabs() {
    return Container(
      height: 40.gh,
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _tabButton('systemMessage'.tr(), 0),
          _tabButton('warning'.tr(), 1),
        ],
      ),
    );
  }

  Widget _tabButton(String title, int index) {
    final isSelected = _selectedTabIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() => _selectedTabIndex = index);
          _fetchNotifications();
        },
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10.gh),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isSelected ? context.theme.primaryColor : Colors.transparent,
                width: 2.gw,
              ),
            ),
          ),
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? context.theme.primaryColor : context.colorTheme.textPrimary,
              fontSize: 14.gsp,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _successState(NotificationsState state) {
    final records = state.notificationData?.records ?? [];
    if (records.isEmpty) {
      return Center(
        child: TableEmptyWidget(
          width: 60.gw,
          height: 60.gh,
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: records.length + (_isLoadingMore ? 1 : 0),
      padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 8.gh),
      itemBuilder: (context, index) {
        if (index == records.length) {
          return Padding(
            padding: EdgeInsets.all(8.gr),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        final record = records[index];
        return NotificationItem(
          isRead: record.status == 1,
          icon: _getIcon(record.type ?? ''),
          title: record.title ?? '',
          subtitle: record.content ?? '',
          color: _getColor(record.type ?? ''),
          onTap: () async {
            await context.read<NotificationsCubit>().readNotification(messageId: record.id ?? 0);
            if (!context.mounted) return;
            context.read<NotificationsCubit>().getNotificationCount();
          },
        );
      },
    );
  }

  // Widget _buildNotificationDetails(BuildContext context, HomeNotificationModel notification) {
  //   return GestureDetector(
  //     onTap: () async {
  //       Navigator.pushNamed(context, routeEventDetails, arguments: notification);
  //     },
  //     child: Container(
  //       margin: EdgeInsets.all(4.gr),
  //       decoration: BoxDecoration(
  //         color: context.theme.cardColor,
  //         borderRadius: BorderRadius.circular(12.gr),
  //       ),
  //       child: Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           _buildNotificationImage(notification.imageUrl),
  //           10.verticalSpace,
  //           Padding(
  //             padding: EdgeInsets.symmetric(horizontal: 10.gw),
  //             child: Text(
  //               notification.title,
  //               style: FontPalette.medium14.copyWith(
  //                 color: context.colorTheme.primary,
  //               ),
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildNotificationImage(String imageUrl) {
  //   return ClipRRect(
  //     borderRadius: BorderRadius.only(
  //       topLeft: Radius.circular(12.gr),
  //       topRight: Radius.circular(12.gr),
  //     ),
  //     child: CachedNetworkImage(
  //       imageUrl: imageUrl,
  //       width: double.infinity,
  //       fadeInDuration: Duration.zero,
  //       fit: BoxFit.contain,
  //       errorWidget: (context, url, error) => const Icon(Icons.broken_image_outlined),
  //       placeholder: (context, url) => ShimmerWidget(
  //         height: 100.gh,
  //         width: double.infinity,
  //         radius: 4.gr,
  //       ),
  //     ),
  //   );
  // }
}

IconData _getIcon(String type) {
  switch (type) {
    case 'system':
      return Icons.notifications;
    case 'announcement':
      return Icons.campaign;
    case 'forecast':
      return Icons.timeline;
    default:
      return Icons.notifications;
  }
}

Color _getColor(String type) {
  switch (type) {
    case 'system':
      return Colors.blue.shade200;
    case 'announcement':
      return Colors.orange.shade200;
    case 'forecast':
      return Colors.green.shade200;
    default:
      return Colors.grey.shade200;
  }
}

Widget _loadingState() {
  return ListView.separated(
    itemCount: 6,
    shrinkWrap: true,
    padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 8.gh),
    separatorBuilder: (_, __) => 10.verticalSpace,
    itemBuilder: (_, __) => ShimmerWidget(
      height: 60.gh,
      radius: 12.gr,
    ),
  );
}
