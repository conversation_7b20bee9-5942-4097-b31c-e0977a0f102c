import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class NotificationItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback? onTap;
  final bool isRead;
  const NotificationItem({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    this.onTap,
    this.isRead = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      color: context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: ListTile(
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.gr),
        ),
        // leading: CircleAvatar(
        //   backgroundColor: color,
        //   child: Icon(icon, color: Colors.white),
        // ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: context.textTheme.primary.copyWith(fontSize: 14.gsp, fontWeight: FontWeight.w400),
            ),
            if (!isRead) ...[
              8.horizontalSpace,
              CircleAvatar(
                radius: 3.gr,
                backgroundColor: context.colorTheme.buttonPrimary,
              ),
            ],
          ],
        ),

        subtitle:
            Text(subtitle, style: context.textTheme.regular.copyWith(fontSize: 12.gsp, fontWeight: FontWeight.w400)),
      ),
    );
  }
}
