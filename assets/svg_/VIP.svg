<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1460_2095)">
<mask id="mask0_1460_2095" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="30" height="30">
<path d="M30 0H0V30H30V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_1460_2095)">
<g filter="url(#filter0_d_1460_2095)">
<path d="M16.0766 4.13335C16.4958 3.89245 16.8245 3.51773 17.0114 3.06766C17.1982 2.6176 17.2327 2.11753 17.1095 1.64549C16.9864 1.17346 16.7124 0.756023 16.3304 0.458348C15.9484 0.160673 15.4798 -0.000494552 14.9979 1.14001e-06C14.516 0.000496832 14.0477 0.162628 13.6663 0.461088C13.2849 0.759549 13.0118 1.17754 12.8895 1.64983C12.7673 2.12212 12.8028 2.62212 12.9906 3.0718C13.1783 3.52148 13.5078 3.89553 13.9274 4.13556L13.9165 4.15546C13.2019 5.66638 12.2092 7.83053 10.6764 8.64291C9.41737 9.3094 7.61073 8.9745 6.26884 8.7247C6.24732 8.41557 6.1407 8.11873 5.96109 7.86788C5.78148 7.61702 5.53606 7.42219 5.25269 7.30549C4.96932 7.18879 4.65933 7.15489 4.35791 7.20764C4.05648 7.26039 3.77568 7.39768 3.54737 7.60392C3.31906 7.81017 3.15238 8.07712 3.06623 8.37448C2.98009 8.67184 2.97793 8.98771 3.06001 9.28625C3.14209 9.58479 3.30512 9.85404 3.5306 10.0635C3.75607 10.2729 4.03497 10.4141 4.33565 10.4711L7.55182 18.8823C7.79033 19.5058 8.20938 20.0418 8.75395 20.4197C9.29852 20.7977 9.94317 20.9999 10.6033 21H19.3965C20.0565 20.9999 20.7012 20.7977 21.2458 20.4197C21.7903 20.0418 22.2094 19.5058 22.4479 18.8823L25.663 10.4711C25.9617 10.4147 26.2391 10.2752 26.4641 10.0681C26.689 9.86107 26.8526 9.59464 26.9364 9.29869C27.0203 9.00275 27.0212 8.68899 26.939 8.39257C26.8567 8.09615 26.6947 7.82878 26.4709 7.62043C26.2471 7.41208 25.9705 7.27097 25.6721 7.21292C25.3736 7.15487 25.0651 7.18217 24.7811 7.29176C24.4971 7.40136 24.2488 7.58891 24.0641 7.83341C23.8793 8.07791 23.7655 8.3697 23.7352 8.67607C22.3617 8.85623 20.5911 9.08613 19.3234 8.41522C17.8211 7.61942 16.8141 5.60448 16.0766 4.13335Z" fill="#3A5BCD"/>
<g filter="url(#filter1_d_1460_2095)">
<path d="M14.9992 15.3392C16.2592 15.3392 17.3392 14.2592 17.3392 12.9992C17.3392 11.7392 16.2592 10.6592 14.9992 10.6592C13.7392 10.6592 12.6592 11.7392 12.6592 12.9992C12.6592 14.2592 13.7392 15.3392 14.9992 15.3392Z" fill="#FF4953"/>
<path d="M14.9992 15.3392C16.2592 15.3392 17.3392 14.2592 17.3392 12.9992C17.3392 11.7392 16.2592 10.6592 14.9992 10.6592C13.7392 10.6592 12.6592 11.7392 12.6592 12.9992C12.6592 14.2592 13.7392 15.3392 14.9992 15.3392Z" fill="#F4F8FF"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_1460_2095" x="-1.6" y="-1.6" width="33.2" height="30.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.665018 0 0 0 0 0.750257 0 0 0 0 0.943685 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1460_2095"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1460_2095" result="shape"/>
</filter>
<filter id="filter1_d_1460_2095" x="12.5512" y="10.6592" width="4.89569" height="4.89569" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.108"/>
<feGaussianBlur stdDeviation="0.054"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.372827 0 0 0 0 0.480685 0 0 0 0 0.725439 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1460_2095"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1460_2095" result="shape"/>
</filter>
<clipPath id="clip0_1460_2095">
<rect width="30" height="30" fill="white"/>
</clipPath>
</defs>
</svg>
