<svg width="91" height="91" viewBox="0 0 91 91" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13158)">
<path d="M35.4048 52.7328L16.9974 55.3394C16.4527 55.4165 15.958 55.6869 15.6208 56.0919L2.40201 71.9654C1.39224 73.178 2.21357 74.9027 3.85116 75.0084L16.6392 75.8343C17.4778 75.8885 18.1862 76.4009 18.4346 77.1331L21.8226 87.1184C22.32 88.5842 24.4253 88.8901 25.4606 87.6469L38.588 71.883C38.9042 71.5034 39.0587 71.0332 39.0217 70.5633L37.7535 54.4382C37.6675 53.3443 36.5934 52.5645 35.4048 52.7328Z" fill="url(#paint0_linear_767_13158)"/>
</g>
<g filter="url(#filter1_ii_767_13158)">
<path d="M53.1616 50.767L72.1093 54.0257C72.6284 54.115 73.0962 54.38 73.419 54.7677L87.6561 71.8641C88.6832 73.0974 87.8146 74.8484 86.1451 74.9103L73.2013 75.3904C72.3145 75.4232 71.5616 75.9685 71.3259 76.7485L68.2429 86.9481C67.7876 88.4543 65.6403 88.7977 64.5869 87.5328L50.4524 70.5596C50.1504 70.197 49.9955 69.7512 50.015 69.3011L50.7422 52.5232C50.7919 51.3774 51.9257 50.5544 53.1616 50.767Z" fill="url(#paint1_linear_767_13158)"/>
</g>
<path d="M43.7591 0.598145L61.2549 10.0537L78.9958 19.1225L78.7507 37.6469L78.9958 56.1713L61.2549 65.2401L43.7591 74.6957L26.2634 65.2401L8.52243 56.1713L8.76759 37.6469L8.52243 19.1225L26.2634 10.0537L43.7591 0.598145Z" fill="url(#paint2_linear_767_13158)"/>
<g filter="url(#filter2_ii_767_13158)">
<path d="M43.7591 5.11621L59.1213 13.4186L74.6987 21.3815L74.4834 37.6468L74.6987 53.9121L59.1213 61.875L43.7591 70.1774L28.397 61.875L12.8196 53.9121L13.0348 37.6468L12.8196 21.3815L28.397 13.4186L43.7591 5.11621Z" fill="url(#paint3_linear_767_13158)"/>
</g>
<g filter="url(#filter3_ii_767_13158)">
<path d="M43.908 9.63428L57.2005 16.8182L70.6793 23.7083L70.493 37.7823L70.6793 51.8563L57.2005 58.7464L43.908 65.9303L30.6154 58.7464L17.1367 51.8563L17.3229 37.7823L17.1367 23.7083L30.6154 16.8182L43.908 9.63428Z" fill="url(#paint4_linear_767_13158)"/>
</g>
<g filter="url(#filter4_d_767_13158)">
<path d="M33.4215 37.647L28.8711 23.189H32.3391L35.4541 35.0976L38.4585 23.189H41.7721L37.376 37.647H33.4215Z" fill="url(#paint5_linear_767_13158)"/>
</g>
<g filter="url(#filter5_d_767_13158)">
<path d="M43.7568 37.6464V23.189H46.734V37.647L43.7568 37.6464Z" fill="url(#paint6_linear_767_13158)"/>
</g>
<g filter="url(#filter6_d_767_13158)">
<path d="M54.8288 23.189C59.0374 23.189 61.6196 24.6932 61.6196 27.8482C61.6196 31.191 58.8936 32.7162 55.115 32.7162H53.2258V37.647H49.7109V23.189H54.8288ZM54.8288 30.4599C56.766 30.4599 57.9849 29.8119 57.9849 27.8482C57.9849 26.1561 56.8372 25.4034 54.7809 25.4034H53.2265V30.4599H54.8288Z" fill="url(#paint7_linear_767_13158)"/>
</g>
<g filter="url(#filter7_d_767_13158)">
<path d="M43.6354 42.165C44.3939 42.165 45.0822 42.2826 45.7002 42.5176C46.3183 42.7396 46.845 43.0791 47.2805 43.5362C47.7159 43.9801 48.046 44.5351 48.2708 45.2011C48.5095 45.854 48.6289 46.6114 48.6289 47.4732C48.6289 48.7268 48.4182 49.8629 47.9969 50.8814C47.5755 51.8869 46.9855 52.7552 46.227 53.4865C45.4685 54.2047 44.5695 54.7597 43.5301 55.1514C42.5047 55.5301 41.3739 55.7195 40.1378 55.7195L40.0957 55.2885C41.2475 55.1841 42.1816 54.8903 42.898 54.4071C43.6143 53.9109 44.1622 53.2841 44.5414 52.5267C44.9347 51.7563 45.2016 50.9206 45.3421 50.0196C45.4825 49.1185 45.5528 48.2175 45.5528 47.3165C45.5528 46.311 45.4615 45.4818 45.2788 44.8289C45.1103 44.176 44.8785 43.6928 44.5835 43.3795C44.2886 43.0661 43.9585 42.9094 43.5933 42.9094C42.919 42.9094 42.4344 43.2162 42.1395 43.83C41.8445 44.4437 41.697 45.3447 41.697 46.533C41.697 47.199 41.7532 47.7605 41.8655 48.2175C41.992 48.6746 42.1886 49.0271 42.4555 49.2752C42.7224 49.5103 43.0735 49.6278 43.509 49.6278C44.0287 49.6278 44.4501 49.4842 44.7732 49.1969C45.0962 48.9096 45.335 48.5113 45.4895 48.0021C45.6441 47.4928 45.7213 46.9117 45.7213 46.2588L46.2481 48.2371C46.1778 48.655 45.9952 49.0467 45.7002 49.4123C45.4193 49.778 45.0401 50.0718 44.5625 50.2938C44.0989 50.5158 43.5582 50.6268 42.9401 50.6268C41.999 50.6268 41.2124 50.4309 40.5803 50.0391C39.9622 49.6343 39.4917 49.1251 39.1686 48.5113C38.8596 47.8845 38.7051 47.2382 38.7051 46.5722C38.7051 45.6059 38.9298 44.7963 39.3793 44.1434C39.8288 43.4904 40.4258 43.0008 41.1702 42.6743C41.9288 42.3348 42.7505 42.165 43.6354 42.165Z" fill="url(#paint8_linear_767_13158)"/>
</g>
<defs>
<filter id="filter0_ii_767_13158" x="1.95996" y="48.71" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13158"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13158" result="effect2_innerShadow_767_13158"/>
</filter>
<filter id="filter1_ii_767_13158" x="50.0137" y="46.7334" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13158"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13158" result="effect2_innerShadow_767_13158"/>
</filter>
<filter id="filter2_ii_767_13158" x="12.8193" y="1.11621" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13158"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13158" result="effect2_innerShadow_767_13158"/>
</filter>
<filter id="filter3_ii_767_13158" x="17.1367" y="4.63428" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13158"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13158" result="effect2_innerShadow_767_13158"/>
</filter>
<filter id="filter4_d_767_13158" x="8.87109" y="13.189" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13158"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13158" result="shape"/>
</filter>
<filter id="filter5_d_767_13158" x="23.7568" y="13.189" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13158"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13158" result="shape"/>
</filter>
<filter id="filter6_d_767_13158" x="29.7109" y="13.189" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13158"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13158" result="shape"/>
</filter>
<filter id="filter7_d_767_13158" x="18.7051" y="32.165" width="49.9238" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13158"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13158" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13158" x1="37.049" y1="52.0456" x2="14.4893" y2="84.7193" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13158" x1="51.3817" y1="49.99" x2="75.4554" y2="84.8565" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13158" x1="43.7591" y1="0.598144" x2="43.7591" y2="74.807" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13158" x1="43.7591" y1="5.11621" x2="43.7591" y2="70.1774" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13158" x1="43.908" y1="9.63428" x2="43.908" y2="65.9303" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13158" x1="35.3216" y1="23.189" x2="35.3216" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13158" x1="45.2454" y1="23.189" x2="45.2454" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13158" x1="55.6653" y1="23.189" x2="55.6653" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13158" x1="43.667" y1="42.165" x2="43.667" y2="55.7195" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>
