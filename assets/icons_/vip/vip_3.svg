<svg width="91" height="90" viewBox="0 0 91 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13085)">
<path d="M35.4048 52.1346L16.9974 54.7413C16.4527 54.8184 15.958 55.0888 15.6208 55.4937L2.40201 71.3673C1.39224 72.5798 2.21357 74.3045 3.85116 74.4103L16.6392 75.2362C17.4778 75.2903 18.1862 75.8028 18.4346 76.535L21.8226 86.5202C22.32 87.9861 24.4253 88.2919 25.4606 87.0487L38.588 71.2849C38.9042 70.9052 39.0587 70.4351 39.0217 69.9651L37.7535 53.84C37.6675 52.7462 36.5934 51.9663 35.4048 52.1346Z" fill="url(#paint0_linear_767_13085)"/>
</g>
<g filter="url(#filter1_ii_767_13085)">
<path d="M53.1616 50.1688L72.1093 53.4276C72.6284 53.5168 73.0962 53.7819 73.419 54.1695L87.6561 71.266C88.6832 72.4992 87.8146 74.2502 86.1451 74.3121L73.2013 74.7922C72.3145 74.8251 71.5616 75.3703 71.3259 76.1503L68.2429 86.35C67.7876 87.8561 65.6403 88.1995 64.5869 86.9346L50.4524 69.9615C50.1504 69.5988 49.9955 69.1531 50.015 68.703L50.7422 51.925C50.7919 50.7793 51.9257 49.9563 53.1616 50.1688Z" fill="url(#paint1_linear_767_13085)"/>
</g>
<path d="M43.7591 0L61.2549 9.45552L78.9958 18.5244L78.7507 37.0488L78.9958 55.5731L61.2549 64.642L43.7591 74.0975L26.2634 64.642L8.52243 55.5731L8.76759 37.0488L8.52243 18.5244L26.2634 9.45552L43.7591 0Z" fill="url(#paint2_linear_767_13085)"/>
<g filter="url(#filter2_ii_767_13085)">
<path d="M43.7591 4.51807L59.1213 12.8205L74.6987 20.7834L74.4834 37.0487L74.6987 53.314L59.1213 61.2769L43.7591 69.5793L28.397 61.2769L12.8196 53.314L13.0348 37.0487L12.8196 20.7834L28.397 12.8205L43.7591 4.51807Z" fill="url(#paint3_linear_767_13085)"/>
</g>
<g filter="url(#filter3_ii_767_13085)">
<path d="M43.908 9.03613L57.2005 16.22L70.6793 23.1101L70.493 37.1842L70.6793 51.2582L57.2005 58.1483L43.908 65.3322L30.6154 58.1483L17.1367 51.2582L17.3229 37.1842L17.1367 23.1101L30.6154 16.22L43.908 9.03613Z" fill="url(#paint4_linear_767_13085)"/>
</g>
<g filter="url(#filter4_d_767_13085)">
<path d="M33.4215 37.0489L28.8711 22.5908H32.3391L35.4541 34.4995L38.4585 22.5908H41.7721L37.376 37.0489H33.4215Z" fill="url(#paint5_linear_767_13085)"/>
</g>
<g filter="url(#filter5_d_767_13085)">
<path d="M43.7568 37.0483V22.5908H46.734V37.0489L43.7568 37.0483Z" fill="url(#paint6_linear_767_13085)"/>
</g>
<g filter="url(#filter6_d_767_13085)">
<path d="M54.8288 22.5908C59.0374 22.5908 61.6196 24.095 61.6196 27.25C61.6196 30.5929 58.8936 32.118 55.115 32.118H53.2258V37.0489H49.7109V22.5908H54.8288ZM54.8288 29.8617C56.766 29.8617 57.9849 29.2137 57.9849 27.25C57.9849 25.558 56.8372 24.8052 54.7809 24.8052H53.2265V29.8617H54.8288Z" fill="url(#paint7_linear_767_13085)"/>
</g>
<g filter="url(#filter7_d_767_13085)">
<path d="M42.5184 55.1213C41.7833 55.1213 41.0997 55.0167 40.4675 54.8075C39.8353 54.5982 39.3281 54.2975 38.9459 53.9051C38.5783 53.5128 38.3945 53.0486 38.3945 52.5124C38.3945 52.094 38.5268 51.7867 38.7915 51.5905C39.0708 51.3813 39.3722 51.2766 39.6957 51.2766C40.122 51.2766 40.4455 51.3747 40.666 51.5709C40.9012 51.754 41.0188 51.9763 41.0188 52.2378C41.0188 52.4732 40.9674 52.6563 40.8645 52.7871C40.7763 52.9178 40.6733 53.029 40.5557 53.1205C40.4675 53.199 40.3867 53.2709 40.3131 53.3363C40.2543 53.4017 40.2249 53.4867 40.2249 53.5913C40.2249 53.8267 40.4161 54.0228 40.7983 54.1798C41.1806 54.3236 41.6216 54.3955 42.1215 54.3955C42.5626 54.3955 43.011 54.2975 43.4667 54.1013C43.9225 53.8921 44.2974 53.5324 44.5914 53.0224C44.9002 52.5124 45.0545 51.8128 45.0545 50.9236C45.0545 50.4136 44.9369 49.9493 44.7017 49.5309C44.4812 49.0993 44.1577 48.7528 43.7314 48.4912C43.3197 48.2297 42.8125 48.0989 42.2097 48.0989C42.0039 48.1512 41.798 48.2101 41.5922 48.2755C41.4011 48.3408 41.21 48.3997 41.0188 48.452L40.7322 48.0204L41.1953 47.8047C41.5922 47.6216 42.0039 47.3666 42.4302 47.0397C42.8713 46.7127 43.2462 46.3204 43.5549 45.8627C43.8637 45.3919 44.0181 44.8492 44.0181 44.2346C44.0181 43.6592 43.871 43.1885 43.577 42.8223C43.2977 42.4561 42.8125 42.2731 42.1215 42.2731C41.6363 42.2731 41.2835 42.3384 41.0629 42.4692C40.8424 42.6 40.7322 42.7308 40.7322 42.8615C40.7322 42.9923 40.7469 43.1035 40.7763 43.195C40.8204 43.2735 40.8718 43.3519 40.9306 43.4304C40.9894 43.5219 41.0409 43.62 41.085 43.7246C41.1438 43.8292 41.1732 43.9731 41.1732 44.1562C41.1732 44.4439 41.0409 44.6727 40.7763 44.8427C40.5263 44.9996 40.2323 45.0781 39.8941 45.0781C39.556 45.0781 39.2767 44.98 39.0561 44.7839C38.8356 44.5877 38.7253 44.3065 38.7253 43.9404C38.7253 43.5742 38.8797 43.2146 39.1884 42.8615C39.5119 42.4954 39.9676 42.1881 40.5557 41.9396C41.1585 41.6911 41.8642 41.5669 42.6728 41.5669C43.4814 41.5669 44.2092 41.6584 44.8561 41.8415C45.503 42.0246 46.0175 42.3058 46.3998 42.685C46.782 43.0642 46.9732 43.5481 46.9732 44.1365C46.9732 44.7773 46.7379 45.3266 46.2675 45.7843C45.8117 46.242 45.1501 46.6212 44.2827 46.922V46.9612C45.106 47.0397 45.8191 47.2554 46.4218 47.6085C47.0246 47.9616 47.4877 48.4062 47.8112 48.9424C48.1493 49.4655 48.3184 50.0409 48.3184 50.6686C48.3184 51.4663 48.1052 52.2051 47.6789 52.8851C47.2525 53.5651 46.6056 54.1078 45.7382 54.5132C44.8855 54.9186 43.8122 55.1213 42.5184 55.1213Z" fill="url(#paint8_linear_767_13085)"/>
</g>
<defs>
<filter id="filter0_ii_767_13085" x="1.95996" y="48.1118" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13085"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13085" result="effect2_innerShadow_767_13085"/>
</filter>
<filter id="filter1_ii_767_13085" x="50.0137" y="46.1353" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13085"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13085" result="effect2_innerShadow_767_13085"/>
</filter>
<filter id="filter2_ii_767_13085" x="12.8193" y="0.518066" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13085"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13085" result="effect2_innerShadow_767_13085"/>
</filter>
<filter id="filter3_ii_767_13085" x="17.1367" y="4.03613" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13085"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13085" result="effect2_innerShadow_767_13085"/>
</filter>
<filter id="filter4_d_767_13085" x="8.87109" y="12.5908" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13085"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13085" result="shape"/>
</filter>
<filter id="filter5_d_767_13085" x="23.7568" y="12.5908" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13085"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13085" result="shape"/>
</filter>
<filter id="filter6_d_767_13085" x="29.7109" y="12.5908" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13085"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13085" result="shape"/>
</filter>
<filter id="filter7_d_767_13085" x="18.3945" y="31.5669" width="49.9238" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13085"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13085" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13085" x1="37.049" y1="51.4475" x2="14.4893" y2="84.1212" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13085" x1="51.3817" y1="49.3918" x2="75.4554" y2="84.2583" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13085" x1="43.7591" y1="-5.83862e-07" x2="43.7591" y2="74.2088" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13085" x1="43.7591" y1="4.51807" x2="43.7591" y2="69.5793" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13085" x1="43.908" y1="9.03613" x2="43.908" y2="65.3322" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13085" x1="35.3216" y1="22.5908" x2="35.3216" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13085" x1="45.2454" y1="22.5908" x2="45.2454" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13085" x1="55.6653" y1="22.5908" x2="55.6653" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13085" x1="43.3565" y1="41.5669" x2="43.3565" y2="55.1213" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>
