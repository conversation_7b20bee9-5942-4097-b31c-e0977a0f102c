<svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1404_268)">
<path d="M36.3496 51.0295L19.7976 54.9173C19.3436 55.0239 18.9487 55.3026 18.6961 55.6947L7.82081 72.5739C7.04658 73.7756 7.91458 75.3568 9.34404 75.3488L21.1115 75.2828C21.8541 75.2786 22.523 75.7309 22.7958 76.4217L26.6733 86.2418C27.2117 87.6052 29.0668 87.7878 29.8607 86.5556L40.6631 69.7894C40.8997 69.4222 40.9947 68.9814 40.9303 68.5493L38.5415 52.5166C38.3863 51.4753 37.3745 50.7888 36.3496 51.0295Z" fill="url(#paint0_linear_1404_268)"/>
<path d="M52.8496 51.0295L69.4016 54.9173C69.8556 55.0239 70.2505 55.3026 70.5031 55.6947L81.3784 72.5739C82.1526 73.7756 81.2846 75.3568 79.8552 75.3488L68.0878 75.2828C67.3451 75.2786 66.6762 75.7309 66.4035 76.4217L62.5259 86.2418C61.9875 87.6052 60.1324 87.7878 59.3385 86.5556L48.5361 69.7894C48.2995 69.4222 48.2045 68.9814 48.2689 68.5493L50.6577 52.5166C50.8129 51.4753 51.8247 50.7888 52.8496 51.0295Z" fill="url(#paint1_linear_1404_268)"/>
<path d="M59.3068 12.5116L59.3246 12.5221L59.3425 12.5322L74.2457 20.8987L74.0397 37.9884L74.0395 38.0091L74.0397 38.0297L74.2457 55.1194L59.3425 63.4859L59.3246 63.496L59.3068 63.5065L44.6096 72.2298L29.9124 63.5065L29.8947 63.496L29.8768 63.4859L14.9736 55.1194L15.1796 38.0297L15.1798 38.0091L15.1796 37.9884L14.9736 20.8987L29.8768 12.5322L29.8947 12.5221L29.9124 12.5116L44.6096 3.78832L59.3068 12.5116Z" fill="url(#paint2_linear_1404_268)" stroke="url(#paint3_linear_1404_268)" stroke-width="3.42"/>
<path d="M56.068 18.06L56.1123 18.0863L56.1572 18.1115L67.7756 24.634L67.6151 37.9571L67.6145 38.0086L67.6151 38.0601L67.7756 51.3832L56.1572 57.9057L56.1123 57.9309L56.068 57.9572L44.6101 64.7578L33.1523 57.9572L33.108 57.9309L33.0631 57.9057L21.4447 51.3832L21.6052 38.0601L21.6058 38.0086L21.6052 37.9571L21.4447 24.634L33.0631 18.1115L33.108 18.0863L33.1523 18.06L44.6101 11.2594L56.068 18.06Z" stroke="url(#paint4_linear_1404_268)" stroke-width="8.55"/>
<g filter="url(#filter0_ii_1404_268)">
<path d="M44.6332 10.7998L56.0855 17.5971L67.6983 24.1164L67.5378 37.4331L67.6983 50.7497L56.0855 57.269L44.6332 64.0663L33.181 57.269L21.5682 50.7497L21.7287 37.4331L21.5682 24.1164L33.181 17.5971L44.6332 10.7998Z" fill="url(#paint5_linear_1404_268)"/>
</g>
<path d="M33.9531 38.138L29.4727 23.0859H32.8873L35.9544 35.4839L38.9125 23.0859H42.1752L37.8467 38.138H33.9531Z" fill="url(#paint6_linear_1404_268)"/>
<path d="M43.5869 38.1383V23.0869H46.8066V38.139L43.5869 38.1383Z" fill="url(#paint7_linear_1404_268)"/>
<path d="M54.635 23.0869C58.4632 23.0869 60.8121 24.6528 60.8121 27.9373C60.8121 31.4174 58.3324 33.0051 54.8953 33.0051H53.1768V38.1383H49.9795V23.0869H54.635ZM54.635 30.6562C56.3971 30.6562 57.5058 29.9816 57.5058 27.9373C57.5058 26.1758 56.4619 25.3922 54.5914 25.3922H53.1774V30.6562H54.635Z" fill="url(#paint8_linear_1404_268)"/>
<path d="M44.2499 56.4632C43.2904 56.4592 42.4706 56.2062 41.7905 55.7042C41.1104 55.2022 40.5902 54.4715 40.2299 53.512C39.8696 52.5526 39.6895 51.3968 39.6895 50.0447C39.6895 48.6966 39.8696 47.5449 40.2299 46.5895C40.5942 45.6341 41.1165 44.9054 41.7966 44.4034C42.4808 43.9014 43.2985 43.6504 44.2499 43.6504C45.2012 43.6504 46.0169 43.9034 46.6971 44.4094C47.3772 44.9114 47.8974 45.6401 48.2577 46.5955C48.622 47.5469 48.8042 48.6966 48.8042 50.0447C48.8042 51.4009 48.624 52.5587 48.2637 53.5181C47.9034 54.4735 47.3832 55.2042 46.7031 55.7103C46.023 56.2123 45.2053 56.4632 44.2499 56.4632ZM44.2499 54.8419C45.0919 54.8419 45.7498 54.431 46.2234 53.6092C46.7011 52.7874 46.94 51.5992 46.94 50.0447C46.94 49.0124 46.8306 48.14 46.612 47.4275C46.3975 46.7109 46.0878 46.1684 45.683 45.8C45.2822 45.4276 44.8045 45.2414 44.2499 45.2414C43.4119 45.2414 42.754 45.6543 42.2763 46.4801C41.7986 47.306 41.5577 48.4942 41.5537 50.0447C41.5537 51.081 41.661 51.9575 41.8755 52.674C42.0941 53.3865 42.4038 53.927 42.8046 54.2954C43.2054 54.6597 43.6871 54.8419 44.2499 54.8419Z" fill="url(#paint9_linear_1404_268)"/>
</g>
<defs>
<filter id="filter0_ii_1404_268" x="21.5684" y="6.5248" width="46.1299" height="61.8166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.275"/>
<feGaussianBlur stdDeviation="8.55"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.579167 0 0 0 0 0.579167 0 0 0 0 0.579167 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1404_268"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.275"/>
<feGaussianBlur stdDeviation="8.55"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.366667 0 0 0 0 0.366667 0 0 0 0 0.366667 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1404_268" result="effect2_innerShadow_1404_268"/>
</filter>
<linearGradient id="paint0_linear_1404_268" x1="37.7309" y1="50.2458" x2="16.9911" y2="82.4355" gradientUnits="userSpaceOnUse">
<stop stop-color="#C8C8C8"/>
<stop offset="0.25" stop-color="#C3C3C3"/>
<stop offset="0.5" stop-color="#D5D5D5"/>
<stop offset="0.625" stop-color="#D5D5D5"/>
<stop offset="1" stop-color="#878787"/>
</linearGradient>
<linearGradient id="paint1_linear_1404_268" x1="51.4683" y1="50.2458" x2="72.2081" y2="82.4355" gradientUnits="userSpaceOnUse">
<stop stop-color="#C8C8C8"/>
<stop offset="0.25" stop-color="#C3C3C3"/>
<stop offset="0.5" stop-color="#D5D5D5"/>
<stop offset="0.625" stop-color="#D5D5D5"/>
<stop offset="1" stop-color="#878787"/>
</linearGradient>
<linearGradient id="paint2_linear_1404_268" x1="44.6096" y1="1.7998" x2="44.6096" y2="74.2183" gradientUnits="userSpaceOnUse">
<stop stop-color="#CFCFCF"/>
<stop offset="0.484375" stop-color="#EEEEEE"/>
<stop offset="1" stop-color="#8F8F8F"/>
</linearGradient>
<linearGradient id="paint3_linear_1404_268" x1="44.6096" y1="1.7998" x2="44.6096" y2="74.2183" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.197072" stop-color="#C6C6C6" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_1404_268" x1="44.6101" y1="6.28809" x2="44.6101" y2="69.7291" gradientUnits="userSpaceOnUse">
<stop stop-color="#BFBFBF"/>
<stop offset="1" stop-color="#D8D8D8"/>
</linearGradient>
<linearGradient id="paint5_linear_1404_268" x1="44.6332" y1="10.7998" x2="44.6332" y2="64.0663" gradientUnits="userSpaceOnUse">
<stop stop-color="#565656"/>
<stop offset="1" stop-color="#565656"/>
</linearGradient>
<linearGradient id="paint6_linear_1404_268" x1="35.8739" y1="23.0859" x2="35.8037" y2="38.1379" gradientUnits="userSpaceOnUse">
<stop offset="0.107585" stop-color="#BFBFBF"/>
<stop offset="0.291667" stop-color="#DFDFDF"/>
<stop offset="0.494792" stop-color="#9E9E9E"/>
<stop offset="0.692708" stop-color="#D8D8D8"/>
<stop offset="0.885417" stop-color="#B3B3B3"/>
<stop offset="1" stop-color="#D7D7D7"/>
</linearGradient>
<linearGradient id="paint7_linear_1404_268" x1="45.2094" y1="23.0869" x2="45.1968" y2="38.139" gradientUnits="userSpaceOnUse">
<stop offset="0.107585" stop-color="#BFBFBF"/>
<stop offset="0.291667" stop-color="#DFDFDF"/>
<stop offset="0.494792" stop-color="#9E9E9E"/>
<stop offset="0.692708" stop-color="#D8D8D8"/>
<stop offset="0.885417" stop-color="#B3B3B3"/>
<stop offset="1" stop-color="#D7D7D7"/>
</linearGradient>
<linearGradient id="paint8_linear_1404_268" x1="55.4385" y1="23.0869" x2="55.3958" y2="38.1383" gradientUnits="userSpaceOnUse">
<stop offset="0.107585" stop-color="#BFBFBF"/>
<stop offset="0.291667" stop-color="#DFDFDF"/>
<stop offset="0.494792" stop-color="#9E9E9E"/>
<stop offset="0.692708" stop-color="#D8D8D8"/>
<stop offset="0.885417" stop-color="#B3B3B3"/>
<stop offset="1" stop-color="#D7D7D7"/>
</linearGradient>
<linearGradient id="paint9_linear_1404_268" x1="40.2808" y1="43.6504" x2="40.2132" y2="56.4632" gradientUnits="userSpaceOnUse">
<stop offset="0.107585" stop-color="#BFBFBF"/>
<stop offset="0.291667" stop-color="#DFDFDF"/>
<stop offset="0.494792" stop-color="#9E9E9E"/>
<stop offset="0.692708" stop-color="#D8D8D8"/>
<stop offset="0.885417" stop-color="#B3B3B3"/>
<stop offset="1" stop-color="#D7D7D7"/>
</linearGradient>
<clipPath id="clip0_1404_268">
<rect width="90" height="90" fill="white"/>
</clipPath>
</defs>
</svg>
