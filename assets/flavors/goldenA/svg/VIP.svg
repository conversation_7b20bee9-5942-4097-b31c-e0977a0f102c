<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="40" height="40" rx="20" fill="#F5EFE5"/>
<g clip-path="url(#clip0_1842_42447)">
<g filter="url(#filter0_d_1842_42447)">
<path d="M28.8949 15.5526C27.6242 15.5526 26.671 16.6117 26.671 17.7765C26.671 18.0943 26.7769 18.3059 26.8829 18.6236L23.2824 20.0002L20.9527 15.235C21.694 14.9173 22.2234 14.0703 22.2234 13.2229C22.2234 11.9522 21.2705 10.999 19.9995 10.999C18.7288 10.999 17.7756 12.0581 17.7756 13.2229C17.7756 14.0701 18.3049 14.9173 19.0463 15.235L16.6107 20.0002L13.1163 18.6236C13.2222 18.4117 13.3282 18.0943 13.3282 17.7765C13.3282 16.5058 12.3753 15.5526 11.1043 15.5526C9.83355 15.5526 8.88037 16.6117 8.88037 17.7765C8.88037 19.0472 9.83327 20.0004 11.1043 20.0004C11.2102 20.0004 11.3162 20.0004 11.5278 20.0004L12.6926 27.0954C12.7984 28.2602 13.7516 29.0016 14.8103 29.0016H25.1879C26.247 29.0016 27.2 28.2602 27.4118 27.0954L28.4709 20.0004C28.5768 20.0004 28.6828 20.0004 28.8944 20.0004C30.1652 20.0004 31.1184 18.9414 31.1184 17.7765C31.1184 16.5058 30.1655 15.5526 28.8944 15.5526H28.8949Z" fill="url(#paint0_linear_1842_42447)"/>
</g>
</g>
<defs>
<filter id="filter0_d_1842_42447" x="4.88037" y="7.99902" width="30.238" height="26.0029" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1842_42447"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1842_42447" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1842_42447" x1="8.88037" y1="20.0003" x2="31.1184" y2="20.0003" gradientUnits="userSpaceOnUse">
<stop stop-color="#F6D199"/>
<stop offset="1" stop-color="#DDB274"/>
</linearGradient>
<clipPath id="clip0_1842_42447">
<rect width="24" height="24" fill="white" transform="translate(8 8)"/>
</clipPath>
</defs>
</svg>
