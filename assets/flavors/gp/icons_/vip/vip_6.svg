<svg width="90" height="91" viewBox="0 0 90 91" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13122)">
<path d="M35.3335 52.7328L16.9261 55.3394C16.3814 55.4165 15.8867 55.6869 15.5495 56.0919L2.33073 71.9654C1.32095 73.178 2.14228 74.9027 3.77987 75.0084L16.5679 75.8343C17.4065 75.8885 18.1149 76.4009 18.3633 77.1331L21.7513 87.1184C22.2487 88.5842 24.354 88.8901 25.3893 87.6469L38.5167 71.883C38.8329 71.5034 38.9874 71.0332 38.9504 70.5633L37.6822 54.4382C37.5962 53.3443 36.5221 52.5645 35.3335 52.7328Z" fill="url(#paint0_linear_767_13122)"/>
</g>
<g filter="url(#filter1_ii_767_13122)">
<path d="M53.0903 50.767L72.0381 54.0257C72.5571 54.115 73.0249 54.38 73.3477 54.7677L87.5849 71.8641C88.6119 73.0974 87.7433 74.8484 86.0738 74.9103L73.13 75.3904C72.2433 75.4232 71.4904 75.9685 71.2546 76.7485L68.1716 86.9481C67.7163 88.4543 65.569 88.7977 64.5156 87.5328L50.3811 70.5596C50.0792 70.197 49.9242 69.7512 49.9437 69.3011L50.6709 52.5232C50.7206 51.3774 51.8544 50.5544 53.0903 50.767Z" fill="url(#paint1_linear_767_13122)"/>
</g>
<path d="M43.6879 0.598145L61.1836 10.0537L78.9246 19.1225L78.6794 37.6469L78.9246 56.1713L61.1836 65.2401L43.6879 74.6957L26.1921 65.2401L8.45114 56.1713L8.6963 37.6469L8.45114 19.1225L26.1921 10.0537L43.6879 0.598145Z" fill="url(#paint2_linear_767_13122)"/>
<g filter="url(#filter2_ii_767_13122)">
<path d="M43.6878 5.11621L59.05 13.4186L74.6274 21.3815L74.4121 37.6468L74.6274 53.9121L59.05 61.875L43.6878 70.1774L28.3257 61.875L12.7483 53.9121L12.9635 37.6468L12.7483 21.3815L28.3257 13.4186L43.6878 5.11621Z" fill="url(#paint3_linear_767_13122)"/>
</g>
<g filter="url(#filter3_ii_767_13122)">
<path d="M43.8367 9.63428L57.1292 16.8182L70.608 23.7083L70.4217 37.7823L70.608 51.8563L57.1292 58.7464L43.8367 65.9303L30.5441 58.7464L17.0654 51.8563L17.2516 37.7823L17.0654 23.7083L30.5441 16.8182L43.8367 9.63428Z" fill="url(#paint4_linear_767_13122)"/>
</g>
<g filter="url(#filter4_d_767_13122)">
<path d="M33.3502 37.647L28.7998 23.189H32.2678L35.3829 35.0976L38.3872 23.189H41.7008L37.3048 37.647H33.3502Z" fill="url(#paint5_linear_767_13122)"/>
</g>
<g filter="url(#filter5_d_767_13122)">
<path d="M43.6855 37.6464V23.189H46.6627V37.647L43.6855 37.6464Z" fill="url(#paint6_linear_767_13122)"/>
</g>
<g filter="url(#filter6_d_767_13122)">
<path d="M54.7576 23.189C58.9661 23.189 61.5483 24.6932 61.5483 27.8482C61.5483 31.191 58.8223 32.7162 55.0438 32.7162H53.1545V37.647H49.6396V23.189H54.7576ZM54.7576 30.4599C56.6947 30.4599 57.9136 29.8119 57.9136 27.8482C57.9136 26.1561 56.7659 25.4034 54.7096 25.4034H53.1552V30.4599H54.7576Z" fill="url(#paint7_linear_767_13122)"/>
</g>
<g filter="url(#filter7_d_767_13122)">
<path d="M43.6967 55.7195C42.9382 55.7195 42.2499 55.6085 41.6318 55.3865C41.0138 55.1514 40.487 54.8119 40.0516 54.3679C39.6161 53.9109 39.279 53.3559 39.0402 52.703C38.8155 52.037 38.7031 51.2731 38.7031 50.4113C38.7031 49.1577 38.9138 48.0282 39.3352 47.0227C39.7566 46.0042 40.3466 45.1358 41.1051 44.4176C41.8636 43.6863 42.7555 43.1313 43.7809 42.7527C44.8204 42.3609 45.9582 42.165 47.1942 42.165L47.2364 42.596C46.0846 42.6874 45.1505 42.9812 44.4341 43.4774C43.7177 43.9736 43.1629 44.6069 42.7696 45.3774C42.3903 46.1347 42.1305 46.9639 41.99 47.8649C41.8495 48.766 41.7793 49.667 41.7793 50.568C41.7793 51.5735 41.8636 52.4027 42.0321 53.0556C42.2148 53.7085 42.4535 54.1917 42.7485 54.505C43.0435 54.8184 43.3736 54.9751 43.7388 54.9751C44.413 54.9751 44.8976 54.6683 45.1926 54.0545C45.4876 53.4408 45.6351 52.5398 45.6351 51.3515C45.6351 50.6855 45.5719 50.124 45.4455 49.667C45.3331 49.1969 45.1435 48.8443 44.8766 48.6093C44.6097 48.3742 44.2585 48.2567 43.8231 48.2567C43.3034 48.2567 42.882 48.4003 42.5589 48.6876C42.2358 48.9749 41.997 49.3732 41.8425 49.8824C41.688 50.3917 41.6108 50.9728 41.6108 51.6257L41.084 49.6474C41.1542 49.2295 41.3298 48.8378 41.6108 48.4722C41.9057 48.1065 42.292 47.8127 42.7696 47.5907C43.2472 47.3687 43.788 47.2577 44.392 47.2577C45.3331 47.2577 46.1127 47.4601 46.7307 47.8649C47.3628 48.2567 47.8334 48.766 48.1424 49.3928C48.4655 50.0065 48.627 50.6463 48.627 51.3123C48.627 52.2656 48.4022 53.0752 47.9528 53.7411C47.5033 54.3941 46.9063 54.8903 46.1618 55.2298C45.4174 55.5562 44.5956 55.7195 43.6967 55.7195Z" fill="url(#paint8_linear_767_13122)"/>
</g>
<defs>
<filter id="filter0_ii_767_13122" x="1.88867" y="48.71" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13122"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13122" result="effect2_innerShadow_767_13122"/>
</filter>
<filter id="filter1_ii_767_13122" x="49.9424" y="46.7334" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13122"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13122" result="effect2_innerShadow_767_13122"/>
</filter>
<filter id="filter2_ii_767_13122" x="12.748" y="1.11621" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13122"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13122" result="effect2_innerShadow_767_13122"/>
</filter>
<filter id="filter3_ii_767_13122" x="17.0654" y="4.63428" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13122"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13122" result="effect2_innerShadow_767_13122"/>
</filter>
<filter id="filter4_d_767_13122" x="8.7998" y="13.189" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13122"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13122" result="shape"/>
</filter>
<filter id="filter5_d_767_13122" x="23.6855" y="13.189" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13122"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13122" result="shape"/>
</filter>
<filter id="filter6_d_767_13122" x="29.6396" y="13.189" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13122"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13122" result="shape"/>
</filter>
<filter id="filter7_d_767_13122" x="18.7031" y="32.165" width="49.9238" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13122"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13122" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13122" x1="36.9777" y1="52.0456" x2="14.418" y2="84.7193" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13122" x1="51.3104" y1="49.99" x2="75.3841" y2="84.8565" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13122" x1="43.6879" y1="0.598144" x2="43.6878" y2="74.807" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13122" x1="43.6878" y1="5.11621" x2="43.6878" y2="70.1774" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13122" x1="43.8367" y1="9.63428" x2="43.8367" y2="65.9303" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13122" x1="35.2503" y1="23.189" x2="35.2503" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13122" x1="45.1741" y1="23.189" x2="45.1741" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13122" x1="55.594" y1="23.189" x2="55.594" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13122" x1="43.6651" y1="42.165" x2="43.6651" y2="55.7195" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>
